﻿using Avalonia;
using Avalonia.Animation;
using Avalonia.Metadata;
using System;
using System.Reactive.Subjects;
using System.Threading;
using System.Threading.Tasks;

namespace Coco.Models.Base
{
    /// <summary>
    /// Tracks the progress of an animation. This class should act the same as <see cref="Animation"/>
    /// </summary>
    public class ControllableAnimationBase : AvaloniaObject, IAnimation
    {
        /// <summary>
        /// Defines the <see cref="Animation"/> property.
        /// </summary>
        public static readonly DirectProperty<ControllableAnimationBase, Avalonia.Animation.Animation> AnimationProperty =
            AvaloniaProperty.RegisterDirect<ControllableAnimationBase, Avalonia.Animation.Animation>(
                nameof(_animation),
                o => o._animation,
                (o, v) => o._animation = v);

        private Avalonia.Animation.Animation _animation;

        [Content]
        public Avalonia.Animation.Animation Animation
        {
            get => _animation;
            set => SetAndRaise(AnimationProperty, ref _animation, value);
        }

        public virtual IDisposable Apply(Animatable control, IClock clock, IObservable<bool> match, Action onComplete = null)
        {
            var previous = false;
            var observable = new Subject<bool>();
            match.Subscribe(b => {
                OnNext(observable, previous, b);
                previous = b;
            });
            return Animation.Apply(control, clock, observable, onComplete);
        }

        internal virtual void OnNext(Subject<bool> match, bool previous, bool obj)
        {
            match.OnNext(obj);
        }

        public virtual Task RunAsync(Animatable control, IClock clock)
        {
            return Animation.RunAsync(control, clock);
        }

        public virtual Task RunAsync(Animatable control, IClock clock, CancellationToken cancellationToken = default)
        {
            return Animation.RunAsync(control, clock, cancellationToken);
        }
    }
}
