using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using Avalonia.Threading;
using Clash.SDK.Models.Enums;
using Clash.SDK.Models.Events;
using Coco.Toolkits;
using System;
using System.ComponentModel;
using System.IO;
using System.Threading.Tasks;

namespace Coco
{
    public partial class MainWindow : FluentWindow
    {
        public MainWindow()
        {
#if COCO_WINDOWS
            this.MinHeight = 550;
            this.MaxHeight = 550;
#elif COCO_OSX
            this.MinHeight = 580;
            this.CanResize = false;
#endif

            InitializeComponent();

            Opened += Loaded;
            Closing += MainWindow_Closing;

#if DEBUG
            this.AttachDevTools();
#endif
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        private async void Loaded(object sender, EventArgs e)
        {
            try
            {
                string clashProfilePath = Path.Combine(DirectoryToolkit.GetClashConfigDir(), "profile.yaml");
                // 下载并启用Clash配置
                if (!File.Exists(clashProfilePath))
                {
                    var userInfo = await Global.RocketMakerClient.GetUserInfo();
                    if (userInfo.Code == 200)
                    {
                        string url = userInfo.Data.SubUrl;
                        await ProfileToolkit.DownloadProfile(url, clashProfilePath);
                        await Global.ClashClient.ReloadClashConfig(ConfigType.Path, false, clashProfilePath);

                        Global.ProfileInited = true;
                    }
                }
                else
                {
                    await Global.ClashClient.ReloadClashConfig(ConfigType.Path, false, clashProfilePath);
                    Global.ProfileInited = true;
                }

                // 初始化WebSocket连接
                Global.ClashClient.GetClashTraffic();
                // 设置流量更新事件
                Global.ClashClient.TrafficReceivedEvt += OnTrafficUpdated;

                // 获取公告
                if (!Global.AnnoShown)
                {
                    var annoResponse = await Global.RocketMakerClient.GetAnnouncement();
                    if (annoResponse.Code == 200 && annoResponse.Data.Count > 0)
                    {
                        var annoData = annoResponse.Data[0];
                        var annoPage = Global.PageToolkit.GetAnnoPage();
                        string date = annoData.Date.ToString("yyyy-MM-dd");

                        annoPage.SetAnnouncement(date, annoData.Markdown.Replace($"{date}\n", ""));
                        await annoPage.ShowDialog(this);


                        Global.AnnoShown = true;
                    }
                }
            }
            catch (Exception ex)
            {
                LogToolkit.Error($"Initialize MainPage failed: {ex.Message}");
            }
        }

        private void MainWindow_Closing(object sender, CancelEventArgs e)
        {
            e.Cancel = true;

            this.Hide();
        }

        /// <summary>
        /// 显示公告按钮
        /// </summary>
        private async void AnnoButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取公告
                var annoResponse = await Global.RocketMakerClient.GetAnnouncement();
                if (annoResponse.Code == 200 && annoResponse.Data.Count > 0)
                {
                    var annoData = annoResponse.Data[0];
                    var annoPage = Global.PageToolkit.GetAnnoPage();
                    string date = annoData.Date.ToString("yyyy-MM-dd");

                    annoPage.SetAnnouncement(date, annoData.Markdown.Replace($"{date}\n", ""));
                    await annoPage.ShowDialog(this);
                }
            }
            catch (Exception ex)
            {
                LogToolkit.Error($"Fetch Announcement failed: {ex.Message}");
            }
        }

        /// <summary>
        /// 流量更新事件
        /// </summary>
        private void OnTrafficUpdated(object sender, TrafficEvtArgs e)
        {
            Task.Run(async () =>
            {
                var traffic = e.Response;
                // 速度统计
                await Dispatcher.UIThread.InvokeAsync(new Action(() =>
                {
                    this.FindControl<TextBlock>("UploadSpeed").Text = FormatToolkit.FormatBytes(traffic.Up, 0);
                    this.FindControl<TextBlock>("DownloadSpeed").Text = FormatToolkit.FormatBytes(traffic.Down, 0);
                }));
            });
        }
    }
}
