﻿using System.IO;

namespace Coco.Toolkits
{
    public class SettingToolkit
    {
        /// <summary>
        /// 获取 Clash 配置文件路径
        /// </summary>
        public static string GetClashConfigPath()
        {
            string path = Path.Combine(DirectoryToolkit.GetClashConfigDir(), "config.yaml");
            return path;
        }

        /// <summary>
        /// 生成 Clash 配置文件
        /// </summary>
        public static void GenerateClashConfig()
        {
            string path = GetClashConfigPath();
            if (!File.Exists(path))
            {
                string decodedString = Base64Toolkit.Decode("bWl4ZWQtcG9ydDogNDczODEK");
                File.WriteAllText(path, decodedString);
            }
        }
    }
}
