﻿using Coco.Toolkits;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Coco.API
{
    public sealed partial class RocketMakerClient : IDisposable
    {
        private static string _baseUrl;
        private static string _baseRoute;
        internal CookieContainer _cookies;
        internal HttpClientHandler _handler;
        internal HttpClient _httpClient;

        public RocketMakerClient()
        {
            _baseUrl = "https://www.mzfclient.com";
            _baseRoute = "/v1";
            _cookies = new CookieContainer();
            _handler = new HttpClientHandler
            {
                CookieContainer = _cookies
            };
            _httpClient = new HttpClient(_handler);
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "Coco/0.0.1");
        }

        public string GetCookies()
        {
            string cookies = string.Empty;

            foreach (var cookie in _cookies.GetCookies(new Uri(API_ENDPOINT)))
            {
                cookies += cookie + "; ";
            }

            return cookies.Trim();
        }

        public void SetCookies(string cookies)
        {
            foreach (var cookie in cookies.Split(";"))
            {
                if (!string.IsNullOrWhiteSpace(cookie))
                {
                    string[] keyValue = cookie.Trim().Split("=");

                    _cookies.Add(new Uri(API_ENDPOINT), new Cookie(keyValue[0], keyValue[1]));
                }
            }
        }

        public string GetBaseUrl()
        {
            return _baseUrl;
        }

        internal async Task<T> GetAsync<T>(string url, Dictionary<string, string> parameters = null)
        {
            string query = "";
            if (parameters != null && parameters.Count > 0)
            {
                query = "?" + string.Join("&", parameters.Select(p => p.Key + "=" + WebUtility.UrlEncode(p.Value)));
                url += query;
            }
            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            string result = await response.Content.ReadAsStringAsync();
            try
            {
                return JsonConvert.DeserializeObject<T>(RC4Toolkit.Decrypt(result));
            }
            catch
            {
                return JsonConvert.DeserializeObject<T>(result);
            }
        }

        internal async Task<T> PostAsync<T>(string url, object postData)
        {
            string json = JsonConvert.SerializeObject(postData);
            var httpContent = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(url, httpContent);
            response.EnsureSuccessStatusCode();
            string result = await response.Content.ReadAsStringAsync();
            try
            {
                return JsonConvert.DeserializeObject<T>(RC4Toolkit.Decrypt(result));
            }
            catch
            {
                return JsonConvert.DeserializeObject<T>(result);
            }
        }

        public void Dispose()
        {
            _httpClient.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
