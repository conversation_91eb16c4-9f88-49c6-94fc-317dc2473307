﻿#if COCO_OSX
using CliWrap;
using System.IO;
using System.Threading.Tasks;
#endif

namespace Coco.Toolkits
{
    public class OSXProxyToolkit
    {
#if COCO_OSX
        private static string toolPath = "/Library/Application Support/Coco/proxy_conf_helper";
#endif

        public static void InitProxyTool()
        {
#if COCO_OSX
            string scriptPath = Path.Combine(DirectoryToolkit.GetCocoScriptDir(), "install_helper.sh");

            // 检测助手是否存在
            if (!File.Exists(toolPath))
            {
                string scriptContent= Base64Toolkit.Decode("IyEvYmluL3NoCgojICBpbnN0YWxsX2hlbHBlci5zaAojICBzaGFkb3dzb2NrcyAmIENvY28KIwojICBDcmVhdGVkIGJ5IGNsb3d3aW5keSBvbiAxNC0zLTE1LgoKY2QgIiQoZGlybmFtZSAiJHtCQVNIX1NPVVJDRVswXX0iKSIKCnN1ZG8gbWtkaXIgLXAgIi9MaWJyYXJ5L0FwcGxpY2F0aW9uIFN1cHBvcnQvQ29jby8iCnN1ZG8gY3AgJHtwcm94eV9jb25mX2hlbHBlcl9wYXRofSAiL0xpYnJhcnkvQXBwbGljYXRpb24gU3VwcG9ydC9Db2NvLyIKc3VkbyBjaG93biByb290OmFkbWluICIvTGlicmFyeS9BcHBsaWNhdGlvbiBTdXBwb3J0L0NvY28vcHJveHlfY29uZl9oZWxwZXIiCnN1ZG8gY2htb2QgYStyeCAiL0xpYnJhcnkvQXBwbGljYXRpb24gU3VwcG9ydC9Db2NvL3Byb3h5X2NvbmZfaGVscGVyIgpzdWRvIGNobW9kICtzICIvTGlicmFyeS9BcHBsaWNhdGlvbiBTdXBwb3J0L0NvY28vcHJveHlfY29uZl9oZWxwZXIiCgplY2hvIGRvbmUK");
                scriptContent = scriptContent.Replace("${proxy_conf_helper_path}", Path.Combine(Global.CocoDir, "proxy_conf_helper"));
                File.WriteAllText(scriptPath, scriptContent);

                Task.Run(async () =>
                {
                    string appleScript = $"do shell script \\\"bash {scriptPath}\\\" with administrator privileges";
                    await Cli.Wrap("osascript").WithArguments($"-e \"{appleScript}\"").ExecuteAsync();
                });
            }
#else
            return;
#endif
        }

        public static void SetProxy()
        {
#if COCO_OSX
            Task.Run(async () =>
            {
                await Cli.Wrap(toolPath).WithArguments($"-m global -l 127.0.0.1 -p {Global.ClashMixedPort} -s 127.0.0.1 -r {Global.ClashMixedPort}").ExecuteAsync();
            });
#else
            return;
#endif
        }

        public static void UnsetProxy()
        {
#if COCO_OSX
            Task.Run(async () =>
            {
                await Cli.Wrap(toolPath).WithArguments("-m off").ExecuteAsync();
            });
#else
            return;
#endif
        }
    }
}
