﻿#if COCO_WINDOWS
using WindowsProxy;
#endif

namespace Coco.Toolkits
{
    public class WindowsProxyToolkit
    {
        public static void SetProxy()
        {
#if COCO_WINDOWS
#pragma warning disable CA1416 // 验证平台兼容性
            using var service = new ProxyService
            {
                Server = $"127.0.0.1:{Global.ClashMixedPort}",
                Bypass = string.Join(@";", ProxyService.LanIp)
            };
            service.Global();
#pragma warning restore CA1416 // 验证平台兼容性
#else
            return;
#endif
        }

        public static void UnsetProxy()
        {
#if COCO_WINDOWS
#pragma warning disable CA1416 // 验证平台兼容性
            using var service = new ProxyService();
            service.Direct();
#pragma warning restore CA1416 // 验证平台兼容性
#else
            return;
#endif
        }
    }
}
