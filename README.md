# 如何给机场进行定制
## 1.修改应用名称
1. 在 ``Name.xamls`` 中将 ``Coco`` 修改为 **机场名称**
2. 在 ``WindowsStartupToolkit.cs`` 中将 ``Coco`` 修改为 **机场名称**
3. 在 ``OSXStartupToolkit.cs`` 中将 ``coco`` 修改为 **机场名称**
4. 在 ``mac-x64.pubxml`` 中将 ``Coco`` 修改为 **机场名称**

## 2.修改应用图标
1. 在 ``Assets`` 替换 ``AppLogo.ico`` 为 **机场图标**
2. 在 ``Assets`` 替换 ``TrayIcon.ico`` 为 **机场图标**
> ``AppLogo.iconset`` 和 ``TrayIcon.iconset`` 中有 ``make.bat`` 可以一键生成图标

## 3.修改 API 地址
1. 在 ``RocketMakerClient.cs`` 中修改 ``_baseUrl`` 和 ``_baseRoute`` 为 **机场地址**