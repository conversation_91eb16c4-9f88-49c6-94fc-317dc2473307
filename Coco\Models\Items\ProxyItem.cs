﻿using Clash.SDK.Models.Enums;

namespace Coco.Models.Items
{
    public class ProxyItem
    {
        public ProxyItem(string name, ProxyType type)
        {
            Name = name;
            Type = type;
        }

        /// <summary>
        /// 代理名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 代理类型
        /// </summary>
        public ProxyType Type { get; set; }

        /// <summary>
        /// 代理延迟
        /// </summary>
        public long Latency { get; set; } = -2;
    }
}
