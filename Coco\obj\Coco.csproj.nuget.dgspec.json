{"format": 1, "restore": {"D:\\Coco_020634\\Coco\\Coco\\Coco.csproj": {}}, "projects": {"D:\\Coco_020634\\Coco\\Coco\\Coco.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Coco_020634\\Coco\\Coco\\Coco.csproj", "projectName": "Coco", "projectPath": "D:\\Coco_020634\\Coco\\Coco\\Coco.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Coco_020634\\Coco\\Coco\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Avalonia": {"target": "Package", "version": "[0.10.999-cibuild0018037-beta, )"}, "Avalonia.Desktop": {"target": "Package", "version": "[0.10.999-cibuild0018037-beta, )"}, "Avalonia.Diagnostics": {"target": "Package", "version": "[0.10.999-cibuild0018037-beta, )"}, "CoelWu.Clash.SDK": {"target": "Package", "version": "[1.3.9, )"}, "NLog": {"target": "Package", "version": "[4.7.13, )"}, "RunAtStartup": {"target": "Package", "version": "[5.0.2, )"}, "WindowsProxy": {"target": "Package", "version": "[5.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}