﻿using Avalonia.Data.Converters;
using System;
using System.Globalization;

namespace Coco.Models.Converters
{
    public class LatencyToTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            long latency = (long)value;
            switch (latency)
            {
                case 0:
                case -1:
                    return "超时";
                case -2:
                    return "";
                default:
                    return $"{latency}ms";
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
