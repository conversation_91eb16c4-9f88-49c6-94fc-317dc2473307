﻿using Avalonia.Data.Converters;
using Avalonia.Media;
using System;
using System.Globalization;

namespace Coco.Models.Converters
{
    public class LatencyToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            long latency = (long)value;
            switch (latency)
            {
                case 0:
                case -1:
                    return new SolidColorBrush(Color.FromRgb(255, 0, 0));
                default:
                    return new SolidColorBrush(Color.FromRgb(0, 128, 0));
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
