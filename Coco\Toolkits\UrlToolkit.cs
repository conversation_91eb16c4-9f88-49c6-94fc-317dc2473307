﻿using System.Diagnostics;
using System.Runtime.InteropServices;

namespace Coco.Toolkits
{
    public class UrlToolkit
    {
        public static void OpenUrl(string url)
        {
            using (Process process = Process.Start(new ProcessStartInfo
            {
                FileName = RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? url : "open",
                Arguments = RuntimeInformation.IsOSPlatform(OSPlatform.OSX) ? $"{url}" : "",
                CreateNoWindow = true,
                UseShellExecute = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
            }));
        }
    }
}
