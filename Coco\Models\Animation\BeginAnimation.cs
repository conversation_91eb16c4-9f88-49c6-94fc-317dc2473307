﻿using Coco.Models.Base;
using System.Reactive.Subjects;

namespace Coco.Models.Animation
{
    public class BeginAnimation : ControllableAnimationBase
    {
        internal override void OnNext(Subject<bool> match, bool previous, bool obj)
        {
            if (obj) return;
            // "Turning" off
            match.OnNext(false);

            // Then "turning" on
            match.OnNext(true);
        }
    }
}
