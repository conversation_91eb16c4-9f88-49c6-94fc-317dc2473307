﻿using Clash.SDK.Extensions;
using Clash.SDK.Models.Enums;
using Clash.SDK.Models.Share;
using Coco.Models.Items;
using System.Collections.Generic;
using System.Linq;

namespace Coco.Toolkits
{
    public class ServerToolkit
    {
        public static List<ProxyItem> GetServerFromClash(List<ClashProxyData> proxies)
        {
            List<ProxyItem> servers = new List<ProxyItem>();

            foreach (var proxy in proxies)
            {
                if (proxy.Type.IsProxy() || proxy.Name.Contains("直接连接"))
                {
                    servers.Add(new ProxyItem(proxy.Name, proxy.Type));
                }
            }

            return servers;
        }

        public static ProxyItem GetCurrentServerFromClash(ModeType mode, List<ClashProxyData> proxies, List<ProxyItem> proxyItems)
        {
            foreach (var proxy in proxies)
            {
                if (proxy.Type == ProxyType.Selector && proxy.Name == (mode == ModeType.Rule ? "🔰国外流量" : "GLOBAL"))
                {
                    return proxyItems.First(p => p.Name == proxy.Now);
                }
            }

            return null;
        }
    }
}
