﻿#if COCO_OSX
using CliWrap;
using System;
using System.IO;
using System.Diagnostics;
using System.Threading.Tasks;
#endif

namespace Coco.Toolkits
{
    public class OSXStartupToolkit
    {
        private static string AppName = "mzfastcloud";

#if COCO_OSX
        private static string PlistData = "PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPCFET0NUWVBFIHBsaXN0IFBVQkxJQyAtLy9BcHBsZSBDb21wdXRlci8vRFREIFBMSVNUIDEuMC8vRU4KaHR0cDovL3d3dy5hcHBsZS5jb20vRFREcy9Qcm9wZXJ0eUxpc3QtMS4wLmR0ZCA+CjxwbGlzdCB2ZXJzaW9uPSIxLjAiPgo8ZGljdD4KICAgIDxrZXk+TGFiZWw8L2tleT4KICAgIDxzdHJpbmc+Y29tLmNvY28uQVBQX05BTUU8L3N0cmluZz4KICAgIDxrZXk+UHJvZ3JhbTwva2V5PgogICAgPHN0cmluZz5BUFBfUEFUSDwvc3RyaW5nPgogICAgPGtleT5SdW5BdExvYWQ8L2tleT4KICAgIDx0cnVlLz4KPC9kaWN0Pgo8L3BsaXN0Pgo=";

        private static string PlistFile = Environment.GetEnvironmentVariable("HOME") + $"/Library/LaunchAgents/com.coco.{AppName}.plist";
#endif

        public static void SetStartup()
        {
#if COCO_OSX
            Task.Run(async () =>
            {
                if (!File.Exists(PlistFile))
                {
                    File.WriteAllText(PlistFile, Base64Toolkit.Decode(PlistData).Replace("APP_NAME", AppName).Replace("APP_PATH", Process.GetCurrentProcess().MainModule.FileName));
                }

                await Cli.Wrap("launchctl").WithArguments($"load -w {PlistFile}").ExecuteAsync();
            });
#else
            return;
#endif
        }

        public static void DeleteStartup()
        {
#if COCO_OSX
            Task.Run(async () =>
            {
                await Cli.Wrap("launchctl").WithArguments($"unload -w {PlistFile}").ExecuteAsync();

                if (File.Exists(PlistFile))
                {
                    File.Delete(PlistFile);
                }
            });
#else
            return;
#endif
        }
    }
}
