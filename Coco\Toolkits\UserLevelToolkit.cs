﻿using Coco.Models.Enums;

namespace Coco.Toolkits
{
    public class UserLevelToolkit
    {
        public static string Parse(UserLevelType level)
        {
            switch (level)
            {
                case UserLevelType.Free:
                    return "免费用户";
                case UserLevelType.Wheat:
                    return "小麦用户";
                case UserLevelType.Barley:
                    return "大麦用户";
                default:
                    return "未知用户";
            }
        }
    }
}
