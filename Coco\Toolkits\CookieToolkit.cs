﻿using System;

namespace Coco.Toolkits
{
    public class CookieToolkit
    {
        public static bool IsExpired(string cookies)
        {
            foreach (var cookie in cookies.Split(";"))
            {
                if (!string.IsNullOrWhiteSpace(cookie))
                {
                    string[] keyValue = cookie.Trim().Split("=");

                    if (keyValue.Length > 1)
                    {
                        if (keyValue[0].Trim() == "expire_in")
                        {
                            long expireTime = long.Parse(keyValue[1].Trim());
                            long currentTime = (DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 10000000;

                            return currentTime > expireTime - 3600;
                        }
                    }
                }
            }

            return true;
        }
    }
}
