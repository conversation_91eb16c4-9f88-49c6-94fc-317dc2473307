<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Coco.Pages.HomePage"
             Background="#EFF3F4">
  <UserControl.Styles>
    <Style Selector="Image.avatar">
      <Setter Property="Width" Value="100" />
      <Setter Property="Height" Value="100" />
    </Style>

    <Style Selector="TextBlock.email">
      <Setter Property="FontFamily" Value="{StaticResource EnglishFont}" />
      <Setter Property="FontSize" Value="18" />
      <Setter Property="FontWeight" Value="Bold" />
      <Setter Property="Foreground" Value="#85000000" />
      <Setter Property="HorizontalAlignment" Value="Center" />
      <Setter Property="Margin" Value="0,20,0,0" />
    </Style>
    
    <Style Selector="TextBlock.user">
      <Setter Property="Foreground" Value="#45000000" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="16" />
      <Setter Property="FontWeight" Value="Regular" />
      <Setter Property="HorizontalAlignment" Value="Center" />
      <Setter Property="Margin" Value="0,10,0,0" />
    </Style>

    <Style Selector="TextBlock.time">
      <Setter Property="Foreground" Value="#45000000" />
      <Setter Property="FontFamily" Value="{StaticResource EnglishFont}" />
      <Setter Property="FontSize" Value="16" />
      <Setter Property="FontWeight" Value="Medium" />
      <Setter Property="HorizontalAlignment" Value="Center" />
      <Setter Property="Margin" Value="0,10,0,0" />
    </Style>

    <Style Selector="Border.top">
      <Setter Property="BorderBrush" Value="#F0F0F0" />
      <Setter Property="BorderThickness" Value="0,1,0,0" />
    </Style>
    
    <Style Selector="Border.leftandtop">
      <Setter Property="BorderBrush" Value="#F0F0F0" />
      <Setter Property="BorderThickness" Value="1,1,0,0" />
    </Style>
    
    <Style Selector="TextBlock.key">
      <Setter Property="Foreground" Value="#95000000" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="16" />
      <Setter Property="HorizontalAlignment" Value="Center" />
      <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    <Style Selector="TextBlock.value">
      <Setter Property="Foreground" Value="#65000000" />
      <Setter Property="FontFamily" Value="{StaticResource EnglishFont}" />
      <Setter Property="HorizontalAlignment" Value="Center" />
      <Setter Property="Margin" Value="0,20,0,0" />
    </Style>

    <Style Selector="ToggleSwitch.all">
      <Setter Property="OnContent" Value="" />
      <Setter Property="OffContent" Value="" />
      <Setter Property="Margin" Value="10,15,0,0" />
    </Style>
    
    <Style Selector="Button.log">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="BorderBrush" Value="Transparent" />
      <Setter Property="Cursor" Value="Hand" />
      <Setter Property="Foreground" Value="#188EFC" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="Margin" Value="0,15,0,0" />
    </Style>

    <Style Selector="Button.logout">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="BorderBrush" Value="Transparent" />
      <Setter Property="Cursor" Value="Hand" />
      <Setter Property="Foreground" Value="#FF4D4F" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="FontWeight" Value="Regular" />
      <Setter Property="Margin" Value="0,10,10,0" />
      <Setter Property="HorizontalAlignment" Value="Right" />
      <Setter Property="VerticalAlignment" Value="Top" />
    </Style>

    <Style Selector="Button:focus /template/ ContentPresenter">
      <Setter Property="Background" Value="Transparent" />
    </Style>

    <Style Selector="Button:pointerover  /template/ ContentPresenter">
      <Setter Property="Background" Value="Transparent" />
    </Style>
    
    <Style Selector="Button:pressed /template/ ContentPresenter">
      <Setter Property="Background" Value="Transparent" />
    </Style>
    
    <Style Selector="Button.log:focus /template/ ContentPresenter">
      <Setter Property="TextBlock.Foreground" Value="#188EFC" />
    </Style>

    <Style Selector="Button.log:pointerover /template/ ContentPresenter">
      <Setter Property="TextBlock.Foreground" Value="#188EFC" />
    </Style>

    <Style Selector="Button.logout:focus /template/ ContentPresenter">
      <Setter Property="TextBlock.Foreground" Value="#FF4D4F" />
    </Style>

    <Style Selector="Button.logout:pointerover /template/ ContentPresenter">
      <Setter Property="TextBlock.Foreground" Value="#FF4D4F" />
    </Style>
    
    <Style Selector="ToggleSwitch.all /template/ Border#OuterBorder">
      <Setter Property="Background" Value="#BFBFBF" />
      <Setter Property="BorderBrush" Value="Transparent" />
    </Style>

    <Style Selector="ToggleSwitch.all /template/ Ellipse#SwitchKnobOff">
      <Setter Property="Fill" Value="#FFFFFF" />
    </Style>
  </UserControl.Styles>
  
  <Grid Background="White" Margin="10">
    <Grid.RowDefinitions>
      <!-- Account Status Area -->
      <RowDefinition />

      <!-- Separator -->
      <RowDefinition Height="20" />
      
      <!-- Clash Status Area -->
      <RowDefinition />
    </Grid.RowDefinitions>

    <!-- Account Status Area -->
    <Grid>
      <Grid.RowDefinitions>
        <!-- Button Row -->
        <RowDefinition Height="40" />

        <!-- Main Row -->
        <RowDefinition />
      </Grid.RowDefinitions>

      <!-- Logout Button -->
      <Button Classes="logout" Content="退出登陆" Click="LogoutButton_Click" />

      <!-- Main Content -->
      <StackPanel Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Center">
        <Image Classes="avatar" Source="avares://Coco/Assets/Avatar.png" />
        
        <TextBlock x:Name="UserEmail" Classes="email" Text="......" />
        
        <TextBlock x:Name="UserLevel" Classes="user" Text="......" />
        
        <TextBlock x:Name="UserExpire" Classes="time" Text="......" />
      </StackPanel>
    </Grid>

    <!-- Clash Status Area -->
    <Grid Grid.Row="2">
      <!-- Row Definitions -->
      <Grid.RowDefinitions>
        <RowDefinition />

        <RowDefinition />
      </Grid.RowDefinitions>

      <!-- Column Definitions -->
      <Grid.ColumnDefinitions>
        <ColumnDefinition />
        
        <ColumnDefinition />
        
        <ColumnDefinition />
      </Grid.ColumnDefinitions>

      <!-- Clash 控制器端口 -->
      <Border Classes="top" Grid.Row="0" Grid.Column="0">
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
          <TextBlock Classes="key" Text="控制器端口" />

          <TextBlock x:Name="ControllerPort" Classes="value" Text="......" />
        </StackPanel>
      </Border>

      <!-- 混合端口 -->
      <Border Classes="leftandtop" Grid.Row="0" Grid.Column="1">
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
          <TextBlock Classes="key" Text="混合端口" />
          
          <TextBlock x:Name="MixedPort" Classes="value" Text="......" />
        </StackPanel>
      </Border>

      <!-- 账户余额 -->
      <Border Classes="leftandtop" Grid.Row="0" Grid.Column="2">
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
          <TextBlock Classes="key" Text="账户余额" />
          
          <TextBlock x:Name="UserBalance" Classes="value" Text="......" />
        </StackPanel>
      </Border>

      <!-- 开机自启 -->
      <Border Classes="top" Grid.Row="1" Grid.Column="0">
        <StackPanel HorizontalAlignment="Center" Margin="0,10,0,0" VerticalAlignment="Center">
          <TextBlock Classes="key" Text="开机自启" />
          
          <ToggleSwitch x:Name="RunAtStartup" Classes="all" Checked="RunAtStartup_Checked" Unchecked="RunAtStartup_Unchecked" />
        </StackPanel>
      </Border>

      <!-- Log 查看 -->
      <Border Classes="leftandtop" Grid.Row="1" Grid.Column="1">
        <StackPanel HorizontalAlignment="Center" Margin="0,10,0,0" VerticalAlignment="Center">
          <TextBlock Classes="key" Text="Log 查看" />

          <Button Classes="log" Content="点击查看" Click="ViewLog_Button" />
        </StackPanel>
      </Border>

      <!-- 保持代理 -->
      <Border Classes="leftandtop" Grid.Row="1" Grid.Column="2">
        <StackPanel HorizontalAlignment="Center" Margin="0,10,0,0" VerticalAlignment="Center">
          <TextBlock Classes="key" Text="保持代理" />

          <ToggleSwitch Classes="all" />
        </StackPanel>
      </Border>
    </Grid>
  </Grid>
</UserControl>
