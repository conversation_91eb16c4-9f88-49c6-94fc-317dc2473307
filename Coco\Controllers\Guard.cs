﻿using Coco.Models.Enums;
using Coco.Toolkits;
using System;
using System.Diagnostics;
using System.IO;

namespace Coco.Controllers
{
    public abstract class Guard
    {
        /// <summary>
        /// 主程序名称
        /// </summary>
        public virtual string Name { get; }

        /// <summary>
        /// 主程序文件名
        /// </summary>
        public virtual string MainFile { get; protected set; }

        /// <summary>
        /// 实例状态
        /// </summary>
        protected StateType State { get; set; } = StateType.Waiting;

        /// <summary>
        /// 进程实例
        /// </summary>
        public Process Instance { get; private set; }

        /// <summary>
        /// 停止进程
        /// </summary>
        public abstract void Stop();

        /// <summary>
        /// 初始化实例
        /// </summary>
        protected virtual void InitInstance(string argument)
        {
            Instance = new Process
            {
                StartInfo =
                {
#if COCO_WINDOWS
                    FileName = Path.GetFullPath(Path.Combine(Global.CocoDir, "Bin", MainFile)),
                    WorkingDirectory = Path.GetFullPath(Path.Combine(Global.CocoDir, "Bin")),
#elif COCO_OSX
                    FileName = Path.GetFullPath(Path.Combine(Global.CocoDir, "../MacOS", MainFile)),
                    WorkingDirectory = Path.GetFullPath(Path.Combine(Global.CocoDir, "../MacOS")),
#endif
                    Arguments = argument,
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                }
            };
        }

        /// <summary>
        /// 停止实例
        /// </summary>
        protected void StopInstance()
        {
            try
            {
                if (Instance == null || Instance.HasExited)
                {
                    return;
                }

                Instance.Kill();
                Instance.WaitForExit();
            }
            catch (Exception ex)
            {
                LogToolkit.Error($"Stop {MainFile} failed: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动实例
        /// </summary>
        protected void StartInstanceAuto(string argument, ProcessPriorityClass priority = ProcessPriorityClass.Normal)
        {
            try
            {
                InitInstance(argument);
                Instance.EnableRaisingEvents = true;
                Instance.Exited += OnExited;

                Instance.Start();
                if (priority != ProcessPriorityClass.Normal)
                {
                    Instance.PriorityClass = priority;
                }
            }
            catch (Exception ex)
            {
                LogToolkit.Error("Start instance failed: {ex.Message}");
            }
        }

        /// <summary>
        /// 退出时执行的操作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void OnExited(object sender, EventArgs e)
        {
            State = StateType.Stopped;
        }
    }
}
