<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
         mc:Ignorable="d" d:DesignWidth="400" d:DesignHeight="180"
        MinWidth="400" MaxWidth="400" MinHeight="180" MaxHeight="180"
        x:Class="Coco.Pages.ConfirmPage"
        Title="ConfirmPage" Background="White" HasSystemDecorations="False"
        ShowInTaskbar="False" WindowStartupLocation="CenterOwner">
  <Window.Styles>
    <Style Selector="Border.confirm">
      <Setter Property="BorderBrush" Value="#45000000" />
      <Setter Property="BorderThickness" Value="1" />
    </Style>

    <Style Selector="Path.icon">
      <Setter Property="Fill" Value="#1890FF" />
      <Setter Property="Stretch" Value="Fill" />
      <Setter Property="Width" Value="22" />
      <Setter Property="Height" Value="22" />
      <Setter Property="VerticalAlignment" Value="Top" />
    </Style>

    <Style Selector="StackPanel.confirm">
      <Setter Property="Width" Value="320" />
      <Setter Property="Margin" Value="10,0,0,0" />
    </Style>

    <Style Selector="TextBlock.title">
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="16" />
    </Style>

    <Style Selector="TextBlock.content">
      <Setter Property="Foreground" Value="#85000000" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="TextWrapping" Value="Wrap" />
      <Setter Property="LineHeight" Value="22" />
      <Setter Property="Margin" Value="0,10,0,0" />
    </Style>

    <Style Selector="Button">
      <Setter Property="Cursor" Value="Hand" />
      <Setter Property="Background" Value="#1890FF" />
      <Setter Property="Foreground" Value="#FFFFFF" />
      <Setter Property="Padding" Value="15,6" />
      <Setter Property="HorizontalAlignment" Value="Right" />
      <Setter Property="HorizontalContentAlignment" Value="Center" />
      <Setter Property="VerticalContentAlignment" Value="Center" />
    </Style>

    <Style Selector="Button:pointerover /template/ ContentPresenter">
      <Setter Property="Background" Value="#1890FF" />
      <Setter Property="TextBlock.Foreground" Value="#FFFFFF" />
    </Style>

    <Style Selector="Button:checked /template/ ContentPresenter">
      <Setter Property="Background" Value="#1890FF" />
      <Setter Property="TextBlock.Foreground" Value="#FFFFFF" />
    </Style>
  </Window.Styles>
  
  <Border Classes="confirm">
    <Grid Margin="20,20,20,0">
      <Grid.RowDefinitions>
        <RowDefinition />

        <RowDefinition />
      </Grid.RowDefinitions>

      <!-- 提示主题 -->
      <StackPanel Orientation="Horizontal">
        <!-- 提示图标 -->
        <Path x:Name="Icon" Classes="icon" Data="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z M464 336a48 48 0 1096 0 48 48 0 10-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z" />

        <!-- 提示文字 -->
        <StackPanel Classes="confirm" Orientation="Vertical">
          <TextBlock x:Name="Title" Classes="title" />

          <TextBlock x:Name="Content" Classes="content" />
        </StackPanel>
      </StackPanel>

      <!-- 确认 & 取消按钮 -->
      <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right">
        <!-- 确认按钮 -->
        <Button Content="Ok" Click="OkButton_Click" />

        <!-- 取消按钮 -->
        <Button Content="Cancel" Margin="10,0,0,0" Click="CancelButton_Click" />
      </StackPanel>
    </Grid>
  </Border>
</Window>
