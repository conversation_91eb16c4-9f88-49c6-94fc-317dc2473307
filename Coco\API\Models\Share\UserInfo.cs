﻿using Coco.Models.Enums;
using Newtonsoft.Json;
using System;

namespace Coco.API.Models.Share
{
    public class UserInfo
    {
        [JsonProperty("username")]
        public string Username { get; set;}

        [JsonProperty("true_name")]
        public string TrueName { get; set;}

        [JsonProperty("traffic")]
        public Traffic Traffic { get; set;}

        [JsonProperty("last_checkin")]
        public DateTime LastCheckin { get; set; }

        [JsonProperty("reg_date")]
        public DateTime RegisterDate { get; set; }

        [JsonProperty("balance")]
        public string Balance { get; set; }

        [JsonProperty("class")]
        public UserLevelType Class { get; set; }

        [JsonProperty("class_expire")]
        public DateTime ClassExpire { get; set; }

        [JsonProperty("pc_sub")]
        public string SubUrl { get; set; }

        [JsonProperty("defaultProxy")]
        public string DefaultProxy { get; set; }
    }
}
