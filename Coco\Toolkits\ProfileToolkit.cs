﻿using System.IO;
using System.Net;
using System.Threading.Tasks;

namespace Coco.Toolkits
{
    public class ProfileToolkit
    {
        public static async Task DownloadProfile(string url, string path)
        {
            var response = await Global.HttpToolkit.GetAsync(url);
            if (response.StatusCode == HttpStatusCode.OK)
            {
                using var reader = new StreamReader(response.Content.ReadAsStream());
                string yamlText = reader.ReadToEnd();

                File.WriteAllText(path, yamlText);
            }
        }
    }
}
