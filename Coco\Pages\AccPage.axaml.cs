using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.Primitives;
using Avalonia.Input;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using Clash.SDK.Models.Enums;
using Coco.Models.Items;
using Coco.Toolkits;
using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;

namespace Coco.Pages
{
    public partial class AccPage : UserControl
    {
        public List<ProxyItem> ProxyItems;

        public AccPage()
        {
            Initialized += AccPage_Initialized;

            InitializeComponent();

            AttachedToVisualTree += Loaded;
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        private void AccPage_Initialized(object sender, EventArgs e)
        {
            this.FindControl<ComboBox>("ServerList").AddHandler(InputElement.PointerWheelChangedEvent, ServerList_WheelChanged, RoutingStrategies.Tunnel);
        }

        private async void Loaded(object sender, VisualTreeAttachmentEventArgs e)
        {
            try
            {
                var clashConfig = await Global.ClashClient.GetClashConfigs();
                if (clashConfig.Mode == ModeType.Rule)
                {
                    this.FindControl<ToggleButton>("SmartProxy").IsChecked = true;
                }
                else if (clashConfig.Mode == ModeType.Global)
                {
                    this.FindControl<ToggleButton>("GlobalProxy").IsChecked = true;
                }

                await WaitInit_Server();

                var userInfo = await Global.RocketMakerClient.GetUserInfo();
                if (userInfo.Code == 200)
                {
                    this.FindControl<TextBlock>("UserTraffic").Text = FormatToolkit.FormatBytes(userInfo.Data.Traffic.Total - userInfo.Data.Traffic.Used);
                    this.FindControl<TextBlock>("UserLevel").Text = UserLevelToolkit.Parse(userInfo.Data.Class);
                    this.FindControl<TextBlock>("UserExpire").Text = userInfo.Data.ClassExpire.ToString("yyyy-MM-dd");
                    this.FindControl<TextBlock>("UserBalance").Text = userInfo.Data.Balance;
                }
            }
            catch (Exception ex)
            {
                LogToolkit.Error($"Initialize AccPage failed: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载服务器函数
        /// </summary>
        private async Task LoadServer()
        {
            var clashConfig = await Global.ClashClient.GetClashConfigs();

            var clashServer = await Global.ClashClient.GetClashProxies();
            ProxyItems = ServerToolkit.GetServerFromClash(clashServer.Proxies);
            this.FindControl<ComboBox>("ServerList").Items = ProxyItems;
            this.FindControl<ComboBox>("ServerList").SelectedItem = ServerToolkit.GetCurrentServerFromClash(clashConfig.Mode, clashServer.Proxies, ProxyItems);
        }

        /// <summary>
        /// 死循环等待服务器初始化
        /// </summary>
        private async Task WaitInit_Server()
        {
            if (!Global.ProfileInited)
            {
                int tryCount = 0;

                while (tryCount < 10)
                {
                    if (!Global.ProfileInited)
                    {
                        await LoadServer();
                    }
                    else
                    {
                        break;
                    }

                    Thread.Sleep(500);
                    tryCount++;
                }
            }
            else
            {
                await LoadServer();
            }
        }

        /// <summary>
        /// 智能代理按钮选中事件
        /// </summary>
        private async void SmartProxy_Checked(object sender, RoutedEventArgs e)
        {
            this.FindControl<ToggleButton>("GlobalProxy").IsChecked = false;

            try
            {
                var dict = new Dictionary<string, dynamic>();
                dict.Add("mode", "Rule");

                await Global.ClashClient.ChangeClashConfigs(dict);
                await LoadServer();
            }
            catch (Exception ex)
            {
                LogToolkit.Error($"Change proxy strategy to Rule failed: {ex.Message}");
            }
        }

        /// <summary>
        /// 全局代理按钮选中事件
        /// </summary>
        private async void GlobalProxy_Checked(object sender, RoutedEventArgs e)
        {
            this.FindControl<ToggleButton>("SmartProxy").IsChecked = false;

            try
            {
                var dict = new Dictionary<string, dynamic>();
                dict.Add("mode", "Global");

                await Global.ClashClient.ChangeClashConfigs(dict);
                await LoadServer();
            }
            catch (Exception ex)
            {
                LogToolkit.Error($"Change proxy strategy to Global failed: {ex.Message}");
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var clashConfig = await Global.ClashClient.GetClashConfigs();

                string clashProfilePath = Path.Combine(DirectoryToolkit.GetClashConfigDir(), "profile.yaml");

                var userInfo = await Global.RocketMakerClient.GetUserInfo();
                if (userInfo.Code == 200)
                {
                    string url = userInfo.Data.SubUrl;
                    await ProfileToolkit.DownloadProfile(url, clashProfilePath);

                    var clashServer = await Global.ClashClient.GetClashProxies();
                    ProxyItems = ServerToolkit.GetServerFromClash(clashServer.Proxies);
                    this.FindControl<ComboBox>("ServerList").Items = ProxyItems;
                    this.FindControl<ComboBox>("ServerList").SelectedItem = ServerToolkit.GetCurrentServerFromClash(clashConfig.Mode, clashServer.Proxies, ProxyItems);
                }
            }
            catch (Exception ex)
            {
                LogToolkit.Error($"Refresh server failed: {ex.Message}");
            }
        }

        private void ServerList_WheelChanged(object sender, PointerWheelEventArgs e)
        {
            ComboBox comboBox = sender as ComboBox;
            if (comboBox != null && comboBox.IsDropDownOpen)
            {
                return;
            }

            e.Handled = true;
        }

        private async void ServerList_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 如果没有选中就不需要重新设置服务器
            if (e.AddedItems.Count <= 0)
            {
                return;
            }

            // 如果不是下拉菜单选中的就不提交服务器修改
            ComboBox comboBox = sender as ComboBox;
            if (!comboBox.IsDropDownOpen)
            {
                return;
            }

            var proxyItemAdded = (ProxyItem)e.AddedItems[0];

            // 防止重复设置服务器
            if (e.RemovedItems.Count > 0)
            {
                var proxyItemRemoved = (ProxyItem)e.RemovedItems[0];
                
                if (proxyItemAdded.Name == proxyItemRemoved.Name)
                {
                    return;
                }
            }

            try
            {
                await Global.ClashClient.DisconnectAllConnections();
                await Global.ClashClient.SwitchClashProxy("🔰国外流量", proxyItemAdded.Name);
                await Global.ClashClient.SwitchClashProxy("GLOBAL", proxyItemAdded.Name);
            }
            catch (Exception ex)
            {
                LogToolkit.Error($"Change server to {proxyItemAdded.Name} failed: {ex.Message}");
            }
        }

        private async void SpeedTest_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Parallel.ForEach(ProxyItems, async server =>
                {
                    if (server.Type != ProxyType.Selector)
                    {
                        var delayResponse = await Global.ClashClient.GetClashProxyDelay(server.Name);
                        server.Latency = delayResponse.DelayLong;
                    }
                });
            }
            catch (Exception ex)
            {
                LogToolkit.Error($"Speed test failed: {ex.Message}");
            }
        }

        /// <summary>
        /// 连接按钮按下事件
        /// </summary>
        private void ConnectButton_Click(object sender, RoutedEventArgs e)
        {
            if (!Global.Connected)
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    WindowsProxyToolkit.SetProxy();
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                {
                    OSXProxyToolkit.SetProxy();
                }

                this.FindControl<Button>("ConnectButton").Content = "断开连接";
                this.FindControl<Button>("ConnectButton").Background = new SolidColorBrush(Color.FromRgb(217, 54, 62));
            }
            else
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    WindowsProxyToolkit.UnsetProxy();
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                {
                    OSXProxyToolkit.UnsetProxy();
                }

                this.FindControl<Button>("ConnectButton").Content = "启动连接";
                this.FindControl<Button>("ConnectButton").Background = new SolidColorBrush(Color.FromRgb(24, 144, 255));
            }

            Global.Connected = !Global.Connected;
        }

        public void OfficialWebsite_Click(object sender, RoutedEventArgs e)
        {
            UrlToolkit.OpenUrl("https://www.mzfast.xyz/user");
        }

        public void PurchasePackage_Click(object sender, RoutedEventArgs e)
        {
            UrlToolkit.OpenUrl("https://www.mzfast.xyz/user/shop");
        }

        public void AffiliateButton_Click(object sender, RoutedEventArgs e)
        {
            UrlToolkit.OpenUrl("https://www.mzfast.xyz/user/invite");
        }
    }
}
