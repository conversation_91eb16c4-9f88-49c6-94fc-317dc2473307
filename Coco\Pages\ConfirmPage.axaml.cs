using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.Shapes;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using Avalonia.Media;

namespace Coco.Pages
{
    public partial class ConfirmPage : Window
    {
        public bool IsAccepted = false;

        public ConfirmPage()
        {
            InitializeComponent();
#if DEBUG
            this.AttachDevTools();
#endif
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        public void SetMessage(string title, string content)
        {
            if (title.Contains("成功"))
            {
                this.FindControl<Path>("Icon").Fill = Brushes.Green;
            }
            else if (title.Contains("失败"))
            {
                this.FindControl<Path>("Icon").Fill = Brushes.Red;
            }

            this.FindControl<TextBlock>("Title").Text = title;
            this.FindControl<TextBlock>("Content").Text = content;
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            IsAccepted = true;
            this.Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            IsAccepted = false;
            this.Close();
        }
    }
}
