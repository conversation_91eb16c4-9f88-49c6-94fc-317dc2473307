﻿#if COCO_WINDOWS
using RunAtStartup;
#endif
using System;
using System.Diagnostics;

namespace Coco.Toolkits
{
    public class WindowsStartupToolkit
    {
        private static string key = "MZFastCloud";

        private static string value = Process.GetCurrentProcess().MainModule.FileName;

#if COCO_WINDOWS
        public static StartupService service = new StartupService(key);
#endif

        public static bool CheckStartup()
        {
#if COCO_WINDOWS
            return service.Check(value);
#else
            return false;
#endif
        }

        public static void SetStartup()
        {
#if COCO_WINDOWS
            try
            {
                service.Set(value);
            }
            catch (Exception e)
            {
                LogToolkit.Error($"Set RunAtStartup: {e.Message}");
            }
#else
            return;
#endif
        }

        public static void DeleteStartup()
        {
#if COCO_WINDOWS
            try
            {
                service.Delete();
            }
            catch (Exception e)
            {
                LogToolkit.Error($"Delete RunAtStartup: {e.Message}");
            }
#else
            return;
#endif
        }
    }
}
