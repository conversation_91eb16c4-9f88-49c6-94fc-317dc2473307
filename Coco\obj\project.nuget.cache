{"version": 2, "dgSpecHash": "50QG168z+SU=", "success": true, "projectFilePath": "D:\\Coco_020634\\Coco\\Coco\\Coco.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\avalonia\\11.0.0-preview1\\avalonia.11.0.0-preview1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.angle.windows.natives\\2.1.0.2020091801\\avalonia.angle.windows.natives.2.1.0.2020091801.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.controls.colorpicker\\11.0.0-preview1\\avalonia.controls.colorpicker.11.0.0-preview1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.controls.datagrid\\11.0.0-preview1\\avalonia.controls.datagrid.11.0.0-preview1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.desktop\\11.0.0-preview1\\avalonia.desktop.11.0.0-preview1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.diagnostics\\11.0.0-preview1\\avalonia.diagnostics.11.0.0-preview1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.freedesktop\\11.0.0-preview1\\avalonia.freedesktop.11.0.0-preview1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.native\\11.0.0-preview1\\avalonia.native.11.0.0-preview1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.remote.protocol\\11.0.0-preview1\\avalonia.remote.protocol.11.0.0-preview1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.skia\\11.0.0-preview1\\avalonia.skia.11.0.0-preview1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.themes.simple\\11.0.0-preview1\\avalonia.themes.simple.11.0.0-preview1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.win32\\11.0.0-preview1\\avalonia.win32.11.0.0-preview1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.x11\\11.0.0-preview1\\avalonia.x11.11.0.0-preview1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\coelwu.clash.sdk\\1.3.9\\coelwu.clash.sdk.1.3.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp\\2.8.2.1-preview.108\\harfbuzzsharp.2.8.2.1-preview.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.linux\\2.8.2.1-preview.108\\harfbuzzsharp.nativeassets.linux.2.8.2.1-preview.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.macos\\2.8.2.1-preview.108\\harfbuzzsharp.nativeassets.macos.2.8.2.1-preview.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.webassembly\\2.8.2.1-preview.108\\harfbuzzsharp.nativeassets.webassembly.2.8.2.1-preview.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.win32\\2.8.2.1-preview.108\\harfbuzzsharp.nativeassets.win32.2.8.2.1-preview.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jetbrains.annotations\\10.3.0\\jetbrains.annotations.10.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.analyzers\\2.9.6\\microsoft.codeanalysis.analyzers.2.9.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\3.4.0\\microsoft.codeanalysis.common.3.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\3.4.0\\microsoft.codeanalysis.csharp.3.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp.scripting\\3.4.0\\microsoft.codeanalysis.csharp.scripting.3.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.scripting.common\\3.4.0\\microsoft.codeanalysis.scripting.common.3.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\5.0.0\\microsoft.netcore.platforms.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.0.1\\microsoft.netcore.targets.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\6.0.0\\microsoft.win32.systemevents.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nlog\\4.7.13\\nlog.4.7.13.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runatstartup\\5.0.2\\runatstartup.5.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp\\2.88.1-preview.108\\skiasharp.2.88.1-preview.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.linux\\2.88.1-preview.108\\skiasharp.nativeassets.linux.2.88.1-preview.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.macos\\2.88.1-preview.108\\skiasharp.nativeassets.macos.2.88.1-preview.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.webassembly\\2.88.1-preview.108\\skiasharp.nativeassets.webassembly.2.88.1-preview.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.win32\\2.88.1-preview.108\\skiasharp.nativeassets.win32.2.88.1-preview.108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\1.5.0\\system.collections.immutable.1.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.annotations\\4.5.0\\system.componentmodel.annotations.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\6.0.0\\system.drawing.common.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.3\\system.memory.4.5.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reactive\\5.0.0\\system.reactive.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.7.0\\system.reflection.emit.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\1.6.0\\system.reflection.metadata.1.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.1.0\\system.runtime.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.6.0\\system.runtime.compilerservices.unsafe.4.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\5.0.0\\system.security.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\4.5.1\\system.text.encoding.codepages.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.channels\\4.7.0\\system.threading.channels.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.3\\system.threading.tasks.extensions.4.5.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.valuetuple\\4.5.0\\system.valuetuple.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\tmds.dbus\\0.9.0\\tmds.dbus.0.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\websocket.client\\4.4.43\\websocket.client.4.4.43.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\windowsproxy\\5.0.6\\windowsproxy.5.0.6.nupkg.sha512"], "logs": [{"code": "NU1603", "level": "Warning", "message": "Coco 依赖于 Avalonia (>= 0.10.999-cibuild0018037-beta)，但没有找到 Avalonia 0.10.999-cibuild0018037-beta。已改为解析 Avalonia 11.0.0-preview1。", "projectPath": "D:\\Coco_020634\\Coco\\Coco\\Coco.csproj", "warningLevel": 1, "filePath": "D:\\Coco_020634\\Coco\\Coco\\Coco.csproj", "libraryId": "Avalonia", "targetGraphs": ["net6.0"]}, {"code": "NU1603", "level": "Warning", "message": "Coco 依赖于 Avalonia.Desktop (>= 0.10.999-cibuild0018037-beta)，但没有找到 Avalonia.Desktop 0.10.999-cibuild0018037-beta。已改为解析 Avalonia.Desktop 11.0.0-preview1。", "projectPath": "D:\\Coco_020634\\Coco\\Coco\\Coco.csproj", "warningLevel": 1, "filePath": "D:\\Coco_020634\\Coco\\Coco\\Coco.csproj", "libraryId": "Avalonia.Desktop", "targetGraphs": ["net6.0"]}, {"code": "NU1603", "level": "Warning", "message": "Coco 依赖于 Avalonia.Diagnostics (>= 0.10.999-cibuild0018037-beta)，但没有找到 Avalonia.Diagnostics 0.10.999-cibuild0018037-beta。已改为解析 Avalonia.Diagnostics 11.0.0-preview1。", "projectPath": "D:\\Coco_020634\\Coco\\Coco\\Coco.csproj", "warningLevel": 1, "filePath": "D:\\Coco_020634\\Coco\\Coco\\Coco.csproj", "libraryId": "Avalonia.Diagnostics", "targetGraphs": ["net6.0"]}]}