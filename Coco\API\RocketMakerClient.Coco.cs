﻿using Coco.API.Models.Responses;
using System.Threading.Tasks;

namespace Coco.API
{
    public sealed partial class RocketMakerClient
    {
        public async Task<LoginResponse> Login(string username, string password)
        {
            var obj = new
            {
                username = username,
                password = password
            };

            var result = await PostAsync<LoginResponse>(API_LOGIN, obj);
            return result;
        }
        
        public async Task<LogoutResponse> Logout()
        {
            var result = await GetAsync<LogoutResponse>(API_LOGOUT);
            return result;
        }

        public async Task<UserInfoResponse> GetUserInfo()
        {
            var result = await GetAsync<UserInfoResponse>(API_INFO);
            return result;
        }

        public async Task<AnnoResponse> GetAnnouncement()
        {
            var result = await GetAsync<AnnoResponse>(API_ANNO);
            return result;
        }
    }
}
