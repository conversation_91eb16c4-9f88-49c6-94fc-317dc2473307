using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;
#if COCO_OSX
using CliWrap;
#endif
using Clash.SDK;
using Coco.Controllers;
using Coco.Toolkits;
using System;
using System.Runtime.InteropServices;

namespace Coco
{
    public class App : Application
    {
        public override void Initialize()
        {
            AvaloniaXamlLoader.Load(this);
        }

        public override async void OnFrameworkInitializationCompleted()
        {
            if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
            {
#if COCO_OSX
                // 开启前杀死占用端口的软件
                await Cli.Wrap("kill").WithArguments($"-9 $(lsof -ti:{Global.ClashControllerPort},{Global.ClashMixedPort})").WithValidation(CommandResultValidation.None).ExecuteAsync();
#endif
                // 读取配置文件
                ConfigToolkit.LoadConfig();
                // 创建Clash核心实例
                Global.ClashController = new ClashController();
                // 启动Clash核心
                Global.ClashController.Start();
                // 设置ClashClient为本机地址
                Global.ClashClient = new ClashClient(Global.ClashControllerPort);
                // 初始化所有页面
                Global.PageToolkit.Init();
                // 初始化macOS代理助手
                if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                {
                    OSXProxyToolkit.InitProxyTool();
                }
                // 设置Cookie
                if (!string.IsNullOrWhiteSpace(Global.Settings.Cookie) && !CookieToolkit.IsExpired(Global.Settings.Cookie))
                {
                    Global.RocketMakerClient.SetCookies(Global.Settings.Cookie);

                    desktop.MainWindow = Global.PageToolkit.GetMainPage();
                }
                else
                {
                    desktop.MainWindow = Global.PageToolkit.GetLoginPage();
                }
            }

            base.OnFrameworkInitializationCompleted();
        }

        public void ShowPage_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(Global.Settings.Cookie))
            {
                Global.PageToolkit.GetMainPage().Show();
            }
            else
            {
                Global.PageToolkit.GetLoginPage().Show();
            }
        }

        public void ExitItem_Click(object sender, EventArgs e)
        {
            Global.ClashController.Stop();

            if (Application.Current.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime lifetime)
            {
                lifetime.Shutdown();
            }
        }
    }
}
