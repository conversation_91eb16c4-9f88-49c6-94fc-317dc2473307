<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:animations="using:Coco.Models.Animation"
             xmlns:converters="using:Coco.Models.Converters"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Coco.Pages.AccPage"
             Background="#EFF3F4">
  <UserControl.Resources>
    <converters:LatencyToColorConverter x:Key="LatencyToColorConverter" />

    <converters:LatencyToTextConverter x:Key="LatencyToTextConverter" />
  </UserControl.Resources>
  
  <UserControl.Styles>
    <Style Selector="ToggleButton.proxyleft">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="BorderBrush" Value="#D9D9D9" />
      <Setter Property="BorderThickness" Value="1,1,0,1" />
      <Setter Property="Foreground" Value="#85000000" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="Width" Value="90" />
      <Setter Property="Height" Value="33" />
      <Setter Property="HorizontalContentAlignment" Value="Center" />
      <Setter Property="VerticalContentAlignment" Value="Center" />
    </Style>

    <Style Selector="ToggleButton.proxyright">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="BorderBrush" Value="#D9D9D9" />
      <Setter Property="BorderThickness" Value="0,1,1,1" />
      <Setter Property="Foreground" Value="#85000000" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="Width" Value="90" />
      <Setter Property="Height" Value="33" />
      <Setter Property="HorizontalContentAlignment" Value="Center" />
      <Setter Property="VerticalContentAlignment" Value="Center" />
    </Style>
    
    <Style Selector="Button.round">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="BorderBrush" Value="#15000000" />
      <Setter Property="BorderThickness" Value="1" />
      <Setter Property="Cursor" Value="Hand" />
      <Setter Property="Width" Value="32" />
      <Setter Property="Height" Value="32" />
      <Setter Property="VerticalAlignment" Value="Top" />
    </Style>

    <Style Selector="PathIcon.icon">
      <Setter Property="Foreground" Value="#65000000" />
    </Style>
    
    <Style Selector="ComboBox.server">
      <Setter Property="BorderBrush" Value="#D9D9D9" />
      <Setter Property="Cursor" Value="Hand" />
      <Setter Property="Foreground" Value="#85000000" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="FontWeight" Value="Regular" />
      <Setter Property="Width" Value="380" />
      <Setter Property="Margin" Value="10,0" />
      <Setter Property="MaxDropDownHeight" Value="250" />
      <Setter Property="HorizontalContentAlignment" Value="Center" />
      <Setter Property="VerticalContentAlignment" Value="Center" />
    </Style>

    <Style Selector="ComboBoxItem">
      <Setter Property="Background" Value="#FFFFFF" />
      <Setter Property="Foreground" Value="#85000000" />
    </Style>

    <Style Selector="TextBlock.server">
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="FontWeight" Value="Regular" />
    </Style>

    <Style Selector="TextBlock.latency">
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="FontWeight" Value="Regular" />
      <Setter Property="Margin" Value="5,0,0,0" />
      <Setter Property="HorizontalAlignment" Value="Right" />
    </Style>
    
    <Style Selector="Image.guage">
      <Setter Property="Width" Value="430" />
      <Setter Property="Height" Value="200" />
    </Style>

    <Style Selector="TextBlock.traffic">
      <Setter Property="Foreground" Value="#C6C8C5" />
      <Setter Property="FontFamily" Value="{StaticResource EnglishFont}" />
      <Setter Property="FontSize" Value="24" />
      <Setter Property="Margin" Value="0,-5,0,0" />
      <Setter Property="HorizontalAlignment" Value="Center" />
      <Setter Property="VerticalAlignment" Value="Bottom" />
    </Style>
    
    <Style Selector="Button.connect">
      <Setter Property="Cursor" Value="Hand" />
      <Setter Property="CornerRadius" Value="16" />
      <Setter Property="Background" Value="#1890FF" />
      <Setter Property="Foreground" Value="#FFFFFF" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="FontWeight" Value="Regular" />
      <Setter Property="Width" Value="230" />
      <Setter Property="Margin" Value="0,0,0,40" />
      <Setter Property="HorizontalAlignment" Value="Center" />
      <Setter Property="HorizontalContentAlignment" Value="Center" />
    </Style>
    
    <Style Selector="Button.web">
      <Setter Property="Cursor" Value="Hand" />
      <Setter Property="Background" Value="#6777EF" />
      <Setter Property="Foreground" Value="White" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="FontWeight" Value="Regular" />
      <Setter Property="Margin" Value="0,20,0,0" />
      <Setter Property="Padding" Value="20,6" />
    </Style>

    <Style Selector="Button.web2">
      <Setter Property="Cursor" Value="Hand" />
      <Setter Property="Background" Value="#FFA426" />
      <Setter Property="Foreground" Value="White" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="FontWeight" Value="Regular" />
      <Setter Property="Margin" Value="0,20,0,0" />
      <Setter Property="Padding" Value="20,6" />
    </Style>

    <Style Selector="Path.icon">
      <Setter Property="Fill" Value="#1890FF" />
      <Setter Property="Stretch" Value="Fill" />
      <Setter Property="Width" Value="14" />
      <Setter Property="Height" Value="14" />
      <Setter Property="Margin" Value="0,5,0,0" />
      <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    <Style Selector="TextBlock.key">
      <Setter Property="Foreground" Value="#65000000" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="FontWeight" Value="Medium" />
    </Style>
    
    <Style Selector="TextBlock.value">
      <Setter Property="Foreground" Value="#1890FF" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="Margin" Value="5,5,0,0" />
    </Style>

    <Style Selector="TextBlock.value2">
      <Setter Property="Foreground" Value="#1890FF" />
      <Setter Property="FontFamily" Value="{StaticResource EnglishFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="Margin" Value="5,5,0,0" />
    </Style>
    
    <Style Selector="Rectangle.leftline">
      <Setter Property="Fill" Value="#F0F0F0" />
      <Setter Property="Height" Value="1" />
      <Setter Property="Margin" Value="10,-2,10,0" />
    </Style>
    
    <Style Selector="Rectangle.rightline">
      <Setter Property="Fill" Value="#F0F0F0" />
      <Setter Property="Height" Value="1" />
      <Setter Property="Margin" Value="10,25,10,0" />
    </Style>
    
    <Style Selector="ComboBoxItem:pointerover /template/ ContentPresenter">
      <Setter Property="Background" Value="#FFFFFF" />
      <Setter Property="TextBlock.Foreground" Value="#85000000" />
    </Style>

    <Style Selector="ComboBoxItem:selected /template/ ContentPresenter">
      <Setter Property="Background" Value="#E6F7FF" />
      <Setter Property="TextBlock.FontWeight" Value="Regular" />
      <Setter Property="TextBlock.Foreground" Value="#85000000" />
    </Style>
    
    <Style Selector="ToggleButton.proxyleft:pointerover /template/ ContentPresenter">
      <Setter Property="Background" Value="#FFFFFF" />
      <Setter Property="BorderBrush" Value="#D9D9D9" />
      <Setter Property="BorderThickness" Value="1,1,0,1" />
      <Setter Property="TextBlock.Foreground" Value="#85000000" />
    </Style>

    <Style Selector="ToggleButton.proxyright:pointerover /template/ ContentPresenter">
      <Setter Property="Background" Value="#FFFFFF" />
      <Setter Property="BorderBrush" Value="#D9D9D9" />
      <Setter Property="BorderThickness" Value="0,1,1,1" />
      <Setter Property="TextBlock.Foreground" Value="#85000000" />
    </Style>
    
    <Style Selector="ToggleButton:checked /template/ ContentPresenter">
      <Setter Property="Background" Value="#1890FF" />
      <Setter Property="BorderBrush" Value="Transparent" />
      <Setter Property="TextBlock.Foreground" Value="#FFFFFF" />
    </Style>
    
    <Style Selector="ToggleButton.proxyleft /template/ ContentPresenter">
      <Setter Property="CornerRadius" Value="2,0,0,2" />
    </Style>

    <Style Selector="ToggleButton.proxyright /template/ ContentPresenter">
      <Setter Property="CornerRadius" Value="0,2,2,0" />
    </Style>
    
    <Style Selector="Button.round /template/ ContentPresenter">
      <Setter Property="CornerRadius" Value="20"/>
    </Style>

    <Style Selector="Button.round:focus /template/ ContentPresenter">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="BorderBrush" Value="#15000000" />
      <Setter Property="BorderThickness" Value="1" />
    </Style>

    <Style Selector="Button.round:pointerover /template/ ContentPresenter">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="BorderBrush" Value="#15000000" />
      <Setter Property="BorderThickness" Value="1" />
    </Style>

    <Style Selector="Button.round:pressed /template/ ContentPresenter">
      <Style.Animations>
        <animations:BeginAnimation>
          <Animation Duration="0:0:5" IterationCount="3">
            <KeyFrame Cue="0%">
              <Setter Property="RotateTransform.Angle" Value="0" />
            </KeyFrame>
            <KeyFrame Cue="100%">
              <Setter Property="RotateTransform.Angle" Value="360" />
            </KeyFrame>
          </Animation>
          </animations:BeginAnimation>
      </Style.Animations>
    </Style>

    <Style Selector="ComboBox:focus /template/ Border">
      <Setter Property="BorderBrush" Value="#D9D9D9" />
    </Style>
    
    <Style Selector="ComboBox:pointerover /template/ Border">
      <Setter Property="BorderBrush" Value="#D9D9D9" />
    </Style>

    <Style Selector="Button.connect:pointerover /template/ ContentPresenter">
      <Setter Property="Background" Value="#1890FF" />
      <Setter Property="TextBlock.Foreground" Value="#FFFFFF" />
    </Style>
    
    <Style Selector="Button.web:focus /template/ ContentPresenter">
      <Setter Property="TextBlock.Foreground" Value="#FFFFFF" />
    </Style>

    <Style Selector="Button.web:pointerover /template/ ContentPresenter">
      <Setter Property="Background" Value="#6777EF" />
      <Setter Property="TextBlock.Foreground" Value="#FFFFFF" />
    </Style>
    
    <Style Selector="Button.web2:pointerover /template/ ContentPresenter">
      <Setter Property="Background" Value="#FFA426" />
      <Setter Property="TextBlock.Foreground" Value="#FFFFFF" />
    </Style>
  </UserControl.Styles>
  
  <Grid>
    <Grid.ColumnDefinitions>
      <!-- 加速面板 -->
      <ColumnDefinition Width="1*" />

      <!-- 账户面板 -->
      <ColumnDefinition Width="170" />
    </Grid.ColumnDefinitions>

    <!-- 加速面板 -->
    <Border Background="White" Margin="10,10,5,10">
      <Grid>
        <!-- Row Definitions -->
        <Grid.RowDefinitions>
          <RowDefinition Height="100" />
          
          <RowDefinition Height="2"/>

          <RowDefinition />
          
          <RowDefinition />

          <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- 代理按钮 -->
        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
          <ToggleButton x:Name="SmartProxy" Classes="proxyleft" Content="智能代理"  Checked="SmartProxy_Checked" />

          <ToggleButton x:Name="GlobalProxy" Classes="proxyright" Content="全局代理" Checked="GlobalProxy_Checked" Margin="-1,0,0,0" />
        </StackPanel>

        <!-- 分割线 -->
        <Rectangle Grid.Row="1" Classes="leftline" VerticalAlignment="Top" />

        <!-- 服务器选择 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
          <!-- 刷新按钮 -->
          <Button Classes="round" Click="RefreshButton_Click">
            <PathIcon Classes="icon" Data="M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z" />
          </Button>

          <!-- 服务器列表 -->
          <ComboBox x:Name="ServerList" Classes="server" SelectionChanged="ServerList_SelectionChanged">
            <ComboBox.ItemTemplate>
              <DataTemplate>
                <Grid>
                  <Grid.ColumnDefinitions>
                    <!-- 服务器名称 -->
                    <ColumnDefinition Width="1*" />

                    <!-- 服务器延迟 -->
                    <ColumnDefinition Width="Auto" />
                  </Grid.ColumnDefinitions>

                  <!-- 服务器名称 -->
                  <TextBlock Classes="server" Text="{Binding Name}" />

                  <!-- 服务器延迟 -->
                  <TextBlock x:Name="Latency" Grid.Column="1" Classes="latency" Text="{Binding Latency, Converter={StaticResource LatencyToTextConverter}}" Foreground="{Binding Latency, Converter={StaticResource LatencyToColorConverter}}" />
                </Grid>
              </DataTemplate>
            </ComboBox.ItemTemplate>
          </ComboBox>

          <!-- 测速按钮 -->
          <Button Classes="round" Click="SpeedTest_Click">
            <PathIcon Classes="icon" Data="M848 359.3H627.7L825.8 109c4.1-5.3.4-13-6.3-13H436c-2.8 0-5.5 1.5-6.9 4L170 547.5c-3.1 5.3.7 12 6.9 12h174.4l-89.4 357.6c-1.9 7.8 7.5 13.3 13.3 7.7L853.5 373c5.2-4.9 1.7-13.7-5.5-13.7zM378.2 732.5l60.3-241H281.1l189.6-327.4h224.6L487 427.4h211L378.2 732.5z" />
          </Button>
        </StackPanel>

        <!-- 仪表盘 -->
        <StackPanel Grid.Row="3" Orientation="Vertical" Margin="0,30,0,0">
          <Image Classes="guage" Stretch="Uniform" Source="avares://Coco/Assets/Gauge.png" />
          
          <!-- 可用流量 -->
          <TextBlock x:Name="UserTraffic" Classes="traffic" Text="......" />
        </StackPanel>
        
        <!-- 连接按钮 -->
        <Grid Grid.Row="4" Margin="0,25,0,0" VerticalAlignment="Bottom">
          <Button x:Name="ConnectButton" Classes="connect" Content="启动连接" Click="ConnectButton_Click" />
        </Grid>
      </Grid>
    </Border>

    <!-- 账户面板 -->
    <Border Grid.Column="1" Background="White" Margin="5,10,10,10">
      <StackPanel>
        <!-- 账户余额 -->
        <StackPanel Orientation="Vertical" Margin="20,20,0,0">
          <TextBlock Classes="key" Text="账户余额" />

          <!-- 图标和文字 -->
          <StackPanel Orientation="Horizontal">
            <Path Classes="icon" Data="M911.5 700.7a8 8 0 00-10.3-4.8L840 718.2V180c0-37.6-30.4-68-68-68H252c-37.6 0-68 30.4-68 68v538.2l-61.3-22.3c-.9-.3-1.8-.5-2.7-.5-4.4 0-8 3.6-8 8V763c0 3.3 2.1 6.3 5.3 7.5L501 910.1c7.1 2.6 14.8 2.6 21.9 0l383.8-139.5c3.2-1.2 5.3-4.2 5.3-7.5v-59.6c0-1-.2-1.9-.5-2.8zM512 837.5l-256-93.1V184h512v560.4l-256 93.1zM660.6 312h-54.5c-3 0-5.8 1.7-7.1 4.4l-84.7 168.8H511l-84.7-168.8a8 8 0 00-7.1-4.4h-55.7c-1.3 0-2.6.3-3.8 1-3.9 2.1-5.3 7-3.2 10.8l103.9 191.6h-57c-4.4 0-8 3.6-8 8v27.1c0 4.4 3.6 8 8 8h76v39h-76c-4.4 0-8 3.6-8 8v27.1c0 4.4 3.6 8 8 8h76V704c0 4.4 3.6 8 8 8h49.9c4.4 0 8-3.6 8-8v-63.5h76.3c4.4 0 8-3.6 8-8v-27.1c0-4.4-3.6-8-8-8h-76.3v-39h76.3c4.4 0 8-3.6 8-8v-27.1c0-4.4-3.6-8-8-8H564l103.7-191.6c.6-1.2 1-2.5 1-3.8-.1-4.3-3.7-7.9-8.1-7.9z" />
            
            <TextBlock x:Name="UserBalance" Classes="value2" Text="......" />
          </StackPanel>
        </StackPanel>

        <!-- 会员等级 -->
        <StackPanel Orientation="Vertical" Margin="20,20,0,0">
          <TextBlock Classes="key" Text="会员等级" />

          <!-- 图标和文字 -->
          <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
            <Path Classes="icon" Data="M913.9 552.2L805 181.4v-.1c-7.6-22.9-25.7-36.5-48.3-36.5-23.4 0-42.5 13.5-49.7 35.2l-71.4 213H388.8l-71.4-213c-7.2-21.7-26.3-35.2-49.7-35.2-23.1 0-42.5 14.8-48.4 36.6L110.5 552.2c-4.4 14.7 1.2 31.4 13.5 40.7l368.5 276.4c2.6 3.6 6.2 6.3 10.4 7.8l8.6 6.4 8.5-6.4c4.9-1.7 9-4.7 11.9-8.9l368.4-275.4c12.4-9.2 18-25.9 13.6-40.6zM751.7 193.4c1-1.8 2.9-1.9 3.5-1.9 1.1 0 2.5.3 3.4 3L818 394.3H684.5l67.2-200.9zm-487.4 1c.9-2.6 2.3-2.9 3.4-2.9 2.7 0 2.9.1 3.4 1.7l67.3 201.2H206.5l57.8-200zM158.8 558.7l28.2-97.3 202.4 270.2-230.6-172.9zm73.9-116.4h122.1l90.8 284.3-212.9-284.3zM512.9 776L405.7 442.3H620L512.9 776zm157.9-333.7h119.5L580 723.1l90.8-280.8zm-40.7 293.9l207.3-276.7 29.5 99.2-236.8 177.5z" />

            <TextBlock x:Name="UserLevel" Classes="value" Text="......"  />
          </StackPanel>
        </StackPanel>

        <!-- 等级过期 -->
        <StackPanel Orientation="Vertical" Margin="20,20,0,0">
          <TextBlock Classes="key" Text="等级过期" />

          <!-- 图标和文字 -->
          <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
            <Path Classes="icon" Data="M512 472a40 40 0 1080 0 40 40 0 10-80 0zm367 352.9L696.3 352V178H768v-68H256v68h71.7v174L145 824.9c-2.8 7.4-4.3 15.2-4.3 23.1 0 35.3 28.7 64 64 64h614.6c7.9 0 15.7-1.5 23.1-4.3 33-12.7 49.4-49.8 36.6-82.8zM395.7 364.7V180h232.6v184.7L719.2 600c-20.7-5.3-42.1-8-63.9-8-61.2 0-119.2 21.5-165.3 60a188.78 188.78 0 01-121.3 43.9c-32.7 0-64.1-8.3-91.8-23.7l118.8-307.5zM210.5 844l41.7-107.8c35.7 18.1 75.4 27.8 116.6 27.8 61.2 0 119.2-21.5 165.3-60 33.9-28.2 76.3-43.9 121.3-43.9 35 0 68.4 9.5 97.6 27.1L813.5 844h-603z" />
            
            <TextBlock x:Name="UserExpire" Classes="value2" Text="......" />
          </StackPanel>
        </StackPanel>

        <!-- 分割线 -->
        <Rectangle Classes="rightline" Width="120" />
        
        <!-- 按钮列表 -->
        <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
          <!-- 最新官网 -->
          <Button Classes="web" Content="最新官网" Click="OfficialWebsite_Click" />

          <!-- 购买套餐 -->
          <Button Classes="web" Content="购买套餐" Click="PurchasePackage_Click" />

          <!-- 邀请返利 -->
          <Button Classes="web2" Content="邀请返利" Click="AffiliateButton_Click" />
        </StackPanel>
      </StackPanel>
    </Border>
  </Grid>
</UserControl>
