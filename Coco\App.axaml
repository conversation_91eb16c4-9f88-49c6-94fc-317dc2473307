<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="Coco.App">
  <Application.Resources>
    <ResourceDictionary>
      <ResourceDictionary.MergedDictionaries>
        <ResourceInclude Source="/Assets/Name.axaml" />
        <ResourceInclude Source="/Assets/Fonts.axaml" />
      </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
  </Application.Resources>
  
  <Application.Styles>
    <FluentTheme Mode="Light" />
    <StyleInclude Source="/Styles/Sidebar.xaml" />
  </Application.Styles>

  <TrayIcon.Icons>
    <TrayIcons>
      <TrayIcon Icon="avares://Coco/Assets/TrayIcon.ico" ToolTipText="{StaticResource ApplicationName}">
        <TrayIcon.Menu>
          <NativeMenu>
            <NativeMenuItem Header="显示主界面" Click="ShowPage_Click" />
            
            <NativeMenuItem Header="退出" Click="ExitItem_Click" />
          </NativeMenu>
        </TrayIcon.Menu>
      </TrayIcon>
    </TrayIcons>
  </TrayIcon.Icons>
</Application>
