using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using Coco.Toolkits;
using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;

namespace Coco.Pages
{
    public partial class HomePage : UserControl
    {
        public HomePage()
        {
            InitializeComponent();

            AttachedToVisualTree += Loaded;
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        private async void Loaded(object sender, VisualTreeAttachmentEventArgs e)
        {
            this.FindControl<TextBlock>("ControllerPort").Text = Convert.ToString(Global.ClashControllerPort);
            this.FindControl<TextBlock>("MixedPort").Text = Convert.ToString(Global.ClashMixedPort);
            this.FindControl<ToggleSwitch>("RunAtStartup").IsChecked = Global.Settings.RunAtStartup;

            try
            {
                var userInfo = await Global.RocketMakerClient.GetUserInfo();
                if (userInfo.Code == 200)
                {
                    this.FindControl<TextBlock>("UserEmail").Text = userInfo.Data.TrueName;
                    this.FindControl<TextBlock>("UserLevel").Text = UserLevelToolkit.Parse(userInfo.Data.Class);
                    this.FindControl<TextBlock>("UserExpire").Text = userInfo.Data.ClassExpire.ToString("yyyy-MM-dd hh:mm:ss");
                    this.FindControl<TextBlock>("UserBalance").Text = userInfo.Data.Balance + " " + "CNY";
                }
            }
            catch (Exception ex)
            {
                LogToolkit.Error($"Initialize HomePage failed: {ex.Message}");
            }
        }

        private async void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var confirmPage = Global.PageToolkit.GetConfirmPage();
            confirmPage.SetMessage("退出登陆", "您确定要退出登陆吗?");
            await confirmPage.ShowDialog(Global.PageToolkit.GetMainPage());

            if (!confirmPage.IsAccepted)
            {
                return;
            }

            try
            {
                await Global.RocketMakerClient.Logout();
                Global.Settings.Cookie = string.Empty;
                ConfigToolkit.SaveSettings();

                Global.PageToolkit.GetLoginPage().Show();
                Global.PageToolkit.GetMainPage().Hide();
            }
            catch (Exception ex)
            {
                LogToolkit.Error($"Logout failed: {ex.Message}");
            }
        }

        private void RunAtStartup_Checked(object sender, RoutedEventArgs e)
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                WindowsStartupToolkit.SetStartup();
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            {
                OSXStartupToolkit.SetStartup();
            }

            Global.Settings.RunAtStartup = true;
        }

        private void RunAtStartup_Unchecked(object sender, RoutedEventArgs e)
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                WindowsStartupToolkit.DeleteStartup();
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            {
                OSXStartupToolkit.DeleteStartup();
            }

            Global.Settings.RunAtStartup = false;
        }

        private void ViewLog_Button(object sender, RoutedEventArgs e)
        {
            try
            {
                Process process = new Process();
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    process.StartInfo.FileName = "notepad.exe";
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                {
                    process.StartInfo.FileName = "open";
                }
                process.StartInfo.Arguments = Path.Combine(DirectoryToolkit.GetCocoLogsDir(), "Application.log");
                process.Start();
            }
            catch (Exception ex)
            {
                LogToolkit.Error($"Open log file failed: {ex.Message}");
            }
        }
    }
}
