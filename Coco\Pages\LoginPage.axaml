<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        MinWidth="785" MaxWidth="785" MinHeight="545" MaxHeight="545"
        x:Class="Coco.Pages.LoginPage"
        Background="White" WindowStartupLocation="CenterScreen">
  <Window.Styles>
    <Style Selector="TextBlock.title">
      <Setter Property="Foreground" Value="White" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="24" />
      <Setter Property="FontWeight" Value="Medium" />
    </Style>

    <Style Selector="TextBlock.subtitle">
      <Setter Property="Foreground" Value="White" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="FontWeight" Value="Regular" />
      <Setter Property="Margin" Value="0,10,0,0" />
    </Style>

    <Style Selector="TextBlock.subtitle2">
      <Setter Property="Foreground" Value="White" />
      <Setter Property="FontFamily" Value="{StaticResource EnglishFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="FontWeight" Value="Light" />
      <Setter Property="Margin" Value="0,5,0,0" />
    </Style>
    
    <Style Selector="Image.background">
      <Setter Property="Width" Value="400" />
      <Setter Property="Stretch" Value="UniformToFill" />
    </Style>
    
    <Style Selector="TextBox.input">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="BorderBrush" Value="#D9D9D9" />
      <Setter Property="BorderThickness" Value="1" />
      <Setter Property="Foreground" Value="#95000000" />
      <Setter Property="FontFamily" Value="{StaticResource EnglishFont}" />
    </Style>

    <Style Selector="TextBox.password">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="BorderBrush" Value="#D9D9D9" />
      <Setter Property="BorderThickness" Value="1" />
      <Setter Property="Foreground" Value="#95000000" />
      <Setter Property="FontFamily" Value="{StaticResource EnglishFont}" />
      <Setter Property="PasswordChar" Value="·" />
    </Style>
    
    <Style Selector="CheckBox.remember">
      <Setter Property="Foreground" Value="#95000000" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
    </Style>
    
    <Style Selector="Button.forget">
      <Setter Property="Cursor" Value="Hand" />
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="Foreground" Value="#1890FF" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="HorizontalAlignment" Value="Right" />
    </Style>
    
    <Style Selector="Button.login">
      <Setter Property="Cursor" Value="Hand" />
      <Setter Property="Background" Value="#1890FF" />
      <Setter Property="Foreground" Value="#FFFFFF" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="HorizontalAlignment" Value="Stretch" />
      <Setter Property="HorizontalContentAlignment" Value="Center" />
    </Style>

    <Style Selector="Button.register">
      <Setter Property="Cursor" Value="Hand" />
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="Foreground" Value="#1890FF" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="Margin" Value="0,-1,0,0" />
      <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    <Style Selector="TextBlock.hint">
      <Setter Property="Foreground" Value="#95000000" />
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    <Style Selector="TextBox:focus /template/ Border#PART_BorderElement">
      <Setter Property="BorderBrush" Value="#40A9FF"/>
      <Setter Property="BorderThickness" Value="1" />
    </Style>
    
    <Style Selector="TextBox:pointerover /template/ Border#PART_BorderElement">
      <Setter Property="BorderBrush" Value="#40A9FF"/>
      <Setter Property="BorderThickness" Value="1" />
    </Style>

    <Style Selector="CheckBox /template/ Border#NormalRectangle">
      <Setter Property="BorderBrush" Value="#1890FF" />
    </Style>
    
    <Style Selector="CheckBox:pointerover /template/ ContentPresenter">
      <Setter Property="TextBlock.Foreground" Value="#95000000" />
    </Style>

    <Style Selector="CheckBox:pointerover /template/ Border#NormalRectangle">
      <Setter Property="BorderBrush" Value="#1890FF" />
    </Style>

    <Style Selector="CheckBox:checked /template/ Border#NormalRectangle">
      <Setter Property="BorderBrush" Value="Transparent" />
      <Setter Property="Background" Value="#1890FF" />
    </Style>
    
    <Style Selector="Button.forget:focus /template/ ContentPresenter">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="TextBlock.Foreground" Value="#1890FF" />
    </Style>
    
    <Style Selector="Button.forget:pointerover /template/ ContentPresenter">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="TextBlock.Foreground" Value="#1890FF" />
    </Style>

    <Style Selector="Button.login:focus /template/ ContentPresenter">
      <Setter Property="Background" Value="#1890FF" />
      <Setter Property="TextBlock.Foreground" Value="#FFFFFF" />
    </Style>

    <Style Selector="Button.login:pointerover /template/ ContentPresenter">
      <Setter Property="Background" Value="#1890FF" />
      <Setter Property="TextBlock.Foreground" Value="#FFFFFF" />
    </Style>
    
    <Style Selector="Button.register:focus /template/ ContentPresenter">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="TextBlock.Foreground" Value="#1890FF" />
    </Style>

    <Style Selector="Button.register:pointerover /template/ ContentPresenter">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="TextBlock.Foreground" Value="#1890FF" />
    </Style>
  </Window.Styles>
  
  <Grid>
    <Grid.ColumnDefinitions>
      <!-- 背景图片 -->
      <ColumnDefinition />

      <!-- 登陆区域 -->
      <ColumnDefinition />
    </Grid.ColumnDefinitions>

    <!-- 背景图片 -->
    <Grid>
      <Image Classes="background" Source="avares://Coco/Assets/Background.jpg" />

      <StackPanel Margin="30,60,0,0">
        <!-- 软件名称 -->
        <TextBlock Classes="title" Text="{StaticResource ApplicationName}" />

        <TextBlock Classes="subtitle" Text="为你打开世界的大门，带你领略世界的风采"  />
        
        <TextBlock Classes="subtitle2" Text="Travel around the world to create a seamless"/>
        
        <TextBlock Classes="subtitle2" Text="experience for you." />
      </StackPanel>
    </Grid>
    
    <!-- 登陆区域 -->
    <Grid Grid.Column="1" Margin="40,80,40,0">
      <Grid.RowDefinitions>
        <!-- 用户名 (邮箱) -->
        <RowDefinition Height="Auto" />

        <!-- 密码 -->
        <RowDefinition Height="Auto" />

        <!-- 记住密码 & 忘记密码 -->
        <RowDefinition Height="Auto" />

        <!-- 登录 -->
        <RowDefinition Height="Auto" />

        <!-- 立即注册 -->
        <RowDefinition Height="Auto" />
      </Grid.RowDefinitions>

      <!-- 用户名 (邮箱) -->
      <TextBox x:Name="Username" Classes="input" Watermark="用户名 (邮箱)" />

      <!-- 密码 -->
      <TextBox Grid.Row="1" x:Name="Password" Classes="password" Watermark="密码" Margin="0,20,0,0" />

      <!-- 记住密码 & 忘记密码 -->
      <Grid Grid.Row="2" Margin="0,20,0,0">
        <Grid.ColumnDefinitions>
          <ColumnDefinition />
          
          <ColumnDefinition />
        </Grid.ColumnDefinitions>

        <!-- 记住密码 -->
        <CheckBox x:Name="RememberPassword" Classes="remember" Content="记住密码" />

        <!-- 忘记密码 -->
        <Button Grid.Column="1" Classes="forget" Content="忘记密码" Click="ForgetButton_Click" />
      </Grid>

      <!-- 登录 -->
      <Button Grid.Row="3" Classes="login" Content="登录" Click="LoginButton_Click" Margin="0,20,0,0" />

      <!-- 立即注册 -->
      <StackPanel Grid.Row="4" Orientation="Horizontal" Margin="0,10,0,0">
        <TextBlock Classes="hint" Text="还没有账号?" />
        
        <Button Classes="register" Content="立即注册" Click="RegisterButton_Click" />
      </StackPanel>
    </Grid>
  </Grid>
</Window>
