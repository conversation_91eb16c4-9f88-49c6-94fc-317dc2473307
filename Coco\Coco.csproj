﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ApplicationIcon>Assets\AppLogo.ico</ApplicationIcon>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(OS)' == 'Windows_NT' ">
    <DefineConstants>COCO_WINDOWS;$(DefineConstants)</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition=" '$([System.Runtime.InteropServices.RuntimeInformation]::IsOSPlatform($([System.Runtime.InteropServices.OSPlatform]::OSX)))' ">
    <DefineConstants>COCO_OSX;$(DefineConstants)</DefineConstants>
  </PropertyGroup>
  
  <ItemGroup>
    <AvaloniaResource Include="Assets\AppLogo.ico" />
    <AvaloniaResource Include="Assets\AppLogo.icns" />
    <AvaloniaResource Include="Assets\Avatar.png" />
    <AvaloniaResource Include="Assets\Background.jpg" />
    <AvaloniaResource Include="Assets\Gauge.png" />
    <AvaloniaResource Include="Assets\TrayIcon.ico" />
    <AvaloniaResource Include="Styles\Sidebar.xaml">
      <Generator>MSBuild:Compile</Generator>
    </AvaloniaResource>
  </ItemGroup>

  <ItemGroup>
    <WinResource Include="Binaries-win64\*.*" />
    <MacResource Include="Binaries-amd64\*.*" />
  </ItemGroup>
  
  <Target Name="CopyMacIcon" AfterTargets="AfterBuild" Condition=" '$(OS)' != 'Windows_NT' ">
    <Copy SourceFiles="Assets\AppLogo.icns" DestinationFolder="$(OutDir)" />
  </Target>

  <Target Name="CopyWinResource" AfterTargets="Publish" Condition=" '$(OS)' == 'Windows_NT' ">
    <Copy SourceFiles="@(WinResource)" DestinationFolder="$(PublishDir)\Bin" />
  </Target>
  
  <Target Name="CopyMacResource" AfterTargets="Publish" Condition=" '$(OS)' != 'Windows_NT' ">
    <Copy SourceFiles="@(MacResource)" DestinationFolder="$(PublishDir)" />
  </Target>
  
  <ItemGroup>
    <PackageReference Include="Avalonia" Version="0.10.999-cibuild0018037-beta" />
    <PackageReference Include="Avalonia.Desktop" Version="0.10.999-cibuild0018037-beta" />
    <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
    <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" Version="0.10.999-cibuild0018037-beta" />
    <PackageReference Include="CoelWu.Clash.SDK" Version="1.3.9" />
    <PackageReference Include="NLog" Version="4.7.13" />
   </ItemGroup>

  <ItemGroup Condition=" '$(OS)' == 'Windows_NT' ">
    <PackageReference Include="RunAtStartup" Version="5.0.2" />
    <PackageReference Include="WindowsProxy" Version="5.0.6" />
  </ItemGroup>

  <ItemGroup Condition=" '$([System.Runtime.InteropServices.RuntimeInformation]::IsOSPlatform($([System.Runtime.InteropServices.OSPlatform]::OSX)))' ">
    <PackageReference Include="CliWrap" Version="3.4.0" />
    <PackageReference Include="Dotnet.Bundle" Version="0.9.13" />
  </ItemGroup>
</Project>
