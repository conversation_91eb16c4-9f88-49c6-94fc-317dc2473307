﻿using Coco.Toolkits;

namespace Coco.Controllers
{
    public class ClashController : Guard
    {
#if COCO_WINDOWS
        public override string MainFile { get; protected set; } = "Clash-Coco.exe";
#else
        public override string MainFile { get; protected set; } = "Clash-Coco";
#endif

        public override string Name { get; } = "Clash";

        public ClashController()
        {

        }

        public void Start()
        {
            string arguments = $"-d \"{DirectoryToolkit.GetClashConfigDir()}\" -ext-ctl 127.0.0.1:{Global.ClashControllerPort}";
            StartInstanceAuto(arguments);
        }

        public override void Stop()
        {
            StopInstance();
        }
    }
}
