﻿using Coco.Models.Others;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.IO;
using System.Text;
using System.Threading;

namespace Coco.Toolkits
{
    public class ConfigToolkit
    {
        private static string SettingsDirectory = DirectoryToolkit.GetCocoSettingsDir();

        private static readonly string SettingsFile = Path.Combine(SettingsDirectory, "config.json");

        private static ReaderWriterLockSlim SettingsWriterLock = new ReaderWriterLockSlim();

        #region 检查创建默认文件

        private static void CreateDirectoryIfNotExists()
        {
            if (!Directory.Exists(SettingsDirectory))
            {
                Directory.CreateDirectory(SettingsDirectory);
            }
        }

        private static void CreateFileIfNotExists()
        {
            if (!File.Exists(SettingsFile))
            {
                using (StreamWriter sw = new StreamWriter(SettingsFile, false, Encoding.UTF8))
                {
                    sw.Write(JsonConvert.SerializeObject(Global.Settings, Formatting.Indented, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }));
                }
            }
        }

        #endregion

        #region 数据库

        private static void CheckAll()
        {
            CheckSettings();
            CheckCountryDatabase();
        }

        private static void CheckSettings()
        {
            SettingToolkit.GenerateClashConfig();
        }

        private static void CheckCountryDatabase()
        {
            try
            {
                // MMDB文件位置
                string path = Path.Combine(DirectoryToolkit.GetClashConfigDir(), "Country.mmdb");
                // 没有MMDB文件就复制一个过去
                if (!File.Exists(path))
                {
#if COCO_WINDOWS
                    string sourcePath = Path.Combine(Global.CocoDir, "Bin", "Country.mmdb");
                    File.Copy(sourcePath, path);
#elif COCO_OSX
                    string sourcePath = Path.Combine(Global.CocoDir, "../Resources", "Country.mmdb");
                    File.Copy(sourcePath, path);
#endif
                }
            }
            catch (Exception ex)
            {
                LogToolkit.Error($"Copy Country.mmdb failed: {ex.Message}");
            }
        }

        #endregion

        #region 读取 & 保存配置

        public static void SaveSettings()
        {
            SettingsWriterLock.EnterWriteLock();
            try
            {
                string content = JsonConvert.SerializeObject(Global.Settings, Formatting.Indented, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                string origin = File.ReadAllText(SettingsFile);
                using (StreamWriter streamWriter = new StreamWriter(SettingsFile, false, Encoding.UTF8))
                {
                    streamWriter.Write(content);
                }
            }
            finally
            {
                SettingsWriterLock.ExitWriteLock();
            }
        }

        public static void LoadConfig()
        {
            CreateDirectoryIfNotExists();
            CreateFileIfNotExists();

            var settingJObject = (JObject)JsonConvert.DeserializeObject(File.ReadAllText(SettingsFile));

            Global.Settings = settingJObject?.ToObject<Settings>();
            if (Global.Settings == null)
            {
                Global.Settings = new Settings();
                SaveSettings();
            }

            CheckAll();
        }

        #endregion
    }
}
