﻿namespace Coco.API
{
    public sealed partial class RocketMakerClient
    {
        private string API_ENDPOINT
        {
            get => _baseUrl + _baseRoute;
        }

        private string API_LOGIN
        {
            get => _baseUrl + _baseRoute + "/login";
        }

        private string API_LOGOUT
        {
            get => _baseUrl + _baseRoute + "/logout";
        }

        private string API_LINK
        {
            get => _baseUrl + _baseRoute + "/link";
        }

        private string API_INFO
        {
            get => _baseUrl + _baseRoute + "/userinfo";
        }

        private string API_ANNO
        {
            get => _baseUrl + _baseRoute + "/anno";
        }
    }
}
