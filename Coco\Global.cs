﻿using Clash.SDK;
using Coco.API;
using Coco.Controllers;
using Coco.Models.Others;
using Coco.Toolkits;
using System;

namespace Coco
{
    public class Global
    {
        #region 常量

        /// <summary>
        /// Coco目录
        /// </summary>
        public static readonly string CocoDir = AppDomain.CurrentDomain.BaseDirectory;

        #endregion

        #region 端口

        /// <summary>
        /// Clash控制器端口
        /// </summary>
        public static int ClashControllerPort = 47380;

        /// <summary>
        /// Clash代理端口
        /// </summary>
        public static int ClashMixedPort = 47381;

        #endregion

        #region 控制器

        /// <summary>
        /// Clash控制器
        /// </summary>
        public static ClashController ClashController = null;

        #endregion

        #region 其他

        /// <summary>
        /// 是否已连接到代理
        /// </summary>
        public static bool Connected = false;

        /// <summary>
        /// 是否已经显示过公告
        /// </summary>
        public static bool AnnoShown = false;

        /// <summary>
        /// 配置是否已经初始化过
        /// </summary>
        public static bool ProfileInited = false;

        /// <summary>
        /// Clash客户端
        /// </summary>
        public static ClashClient ClashClient = null;

        /// <summary>
        /// Rocket Maker客户端
        /// </summary>
        public static RocketMakerClient RocketMakerClient = new RocketMakerClient();

        /// <summary>
        /// 用于读取和写入的设置
        /// </summary>
        public static Settings Settings = new Settings();

        /// <summary>
        /// Http客户端
        /// </summary>
        public static HttpToolkit HttpToolkit = new HttpToolkit();

        /// <summary>
        /// 用于储存页面
        /// </summary>
        public static PageToolkit PageToolkit = new PageToolkit();

        #endregion

    }
}
