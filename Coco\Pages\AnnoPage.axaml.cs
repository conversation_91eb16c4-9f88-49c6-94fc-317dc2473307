using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;

namespace Coco.Pages
{
    public partial class AnnoPage : Window
    {

        public AnnoPage()
        {
            InitializeComponent();
#if DEBUG
            this.AttachDevTools();
#endif
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        public void SetAnnouncement(string title, string content)
        {
            this.FindControl<TextBlock>("Title").Text = "[公告]" + title;
            this.FindControl<TextBlock>("Content").Text = content;
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
