using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using Coco.Toolkits;
using System;

namespace Coco.Pages
{
    public partial class LoginPage : Window
    {
        public LoginPage()
        {
            Opened += Loaded;

            InitializeComponent();

            ExtendClientAreaToDecorationsHint = true;
            ExtendClientAreaTitleBarHeightHint = -1;
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        private void Loaded(object sender, EventArgs e)
        {
            string username = Global.Settings.Username;
            string password = Global.Settings.Password;
            bool remember = Global.Settings.RememberPassword;

            if (!string.IsNullOrWhiteSpace(username))
            {
                this.FindControl<TextBox>("Username").Text = username;
            }
            if (!string.IsNullOrWhiteSpace(password))
            {
                this.FindControl<TextBox>("Password").Text = password;
            }
            if (remember)
            {
                this.FindControl<CheckBox>("RememberPassword").IsChecked = true;
            }
        }

        private void ForgetButton_Click(object sender, RoutedEventArgs e)
        {
            UrlToolkit.OpenUrl("https://www.mzfast.xyz/password/reset");
        }

        private void SaveCredential(string username, string password, string cookies)
        {
            Global.Settings.Username = username;

            bool rememberPassword = (bool)this.FindControl<CheckBox>("RememberPassword").IsChecked;
            if (rememberPassword)
            {
                Global.Settings.Password = password;
                Global.Settings.RememberPassword = true;
            }

            Global.Settings.Cookie = cookies;
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            var confirmPage = Global.PageToolkit.GetConfirmPage();

            try
            {
                string username = this.FindControl<TextBox>("Username").Text;
                string password = this.FindControl<TextBox>("Password").Text;
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    confirmPage.SetMessage("登陆失败", "账户名或密码不能为空");
                    await confirmPage.ShowDialog(this);
                    return;
                }

                var loginResponse = await Global.RocketMakerClient.Login(username, password);
                if (loginResponse.Code == 200)
                {
                    SaveCredential(username, password, Global.RocketMakerClient.GetCookies());
                    ConfigToolkit.SaveSettings();

                    // 显示提示框
                    confirmPage.SetMessage("登陆成功", "关闭此窗口后自动进入客户端");
                    await confirmPage.ShowDialog(this);

                    // 显示主界面
                    Global.PageToolkit.GetMainPage().Show();
                    Global.PageToolkit.GetLoginPage().Hide();
                }
                else
                {
                    confirmPage.SetMessage("登陆失败", loginResponse.Message);
                    await confirmPage.ShowDialog(this);
                }
            }
            catch (Exception ex)
            {
                LogToolkit.Error($"Login failed: {ex.Message}");

                confirmPage.SetMessage("登陆失败", ex.Message);
                await confirmPage.ShowDialog(this);
            }
        }

        private void RegisterButton_Click(object sender, RoutedEventArgs e)
        {
            UrlToolkit.OpenUrl("https://www.mzfast.xyz/auth/register");
        }
    }
}
