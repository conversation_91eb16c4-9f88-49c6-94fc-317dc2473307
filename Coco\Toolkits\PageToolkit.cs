﻿using Coco.Pages;

namespace Coco.Toolkits
{
    public class PageToolkit
    {
        private MainWindow _mainPage;

        private LoginPage _loginPage;

        private AnnoPage _annoPage;

        private ConfirmPage _confirmPage;

        public void Init()
        {
            _mainPage = new MainWindow();
            _loginPage = new LoginPage();
        }

        public MainWindow GetMainPage()
        {
            return _mainPage;
        }

        public LoginPage GetLoginPage()
        {
            return _loginPage;
        }

        public AnnoPage GetAnnoPage()
        {
            _annoPage = new AnnoPage();
            return _annoPage;
        }

        public ConfirmPage GetConfirmPage()
        {
            _confirmPage = new ConfirmPage();
            return _confirmPage;
        }
    }
}
