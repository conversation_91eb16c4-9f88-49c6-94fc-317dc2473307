﻿using System;
using System.IO;

namespace Coco.Toolkits
{
    public class DirectoryToolkit
    {
        /// <summary>
        /// 获取用户目录
        /// </summary>
        public static string GetUserHomeDir()
        {
#if COCO_WINDOWS
            string path = Directory.GetParent(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)).FullName;
            if (Environment.OSVersion.Version.Major >= 6)
            {
                path = Directory.GetParent(path).ToString();
            }

            return path;
#elif COCO_OSX
            string path = Environment.GetEnvironmentVariable("HOME");

            return path;
#endif
        }

        /// <summary>
        /// 获取Coco根目录
        /// </summary>
        public static string GetCocoDir()
        {
            return Path.Combine(GetUserHomeDir(), ".config", "coco");
        }

        /// <summary>
        /// 获取Clash配置目录
        /// </summary>
        public static string GetClashConfigDir()
        {
            string path = Path.Combine(GetCocoDir(), "clash");
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }


            return path;
        }

        /// <summary>
        /// 获取Coco设置目录
        /// </summary>
        public static string GetCocoSettingsDir()
        {
            string path = Path.Combine(GetCocoDir(), "settings");
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }

            return path;
        }

        /// <summary>
        /// 获取Coco脚本目录
        /// </summary>
        public static string GetCocoScriptDir()
        {
            string path = Path.Combine(GetCocoDir(), "scripts");
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }

            return path;
        }

        /// <summary>
        /// 获取Coco日志文件目录
        /// </summary>
        public static string GetCocoLogsDir()
        {
            string path = Path.Combine(GetCocoDir(), "logs");
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }

            return path;
        }
    }
}
