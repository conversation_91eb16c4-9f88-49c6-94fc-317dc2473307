﻿using NLog;
using NLog.Config;
using NLog.Targets;
using NLog.Targets.Wrappers;
using System.IO;

namespace Coco.Toolkits
{
    public static class LogToolkit
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        static LogToolkit()
        {
            var configuration = new LoggingConfiguration();

            var logFile = new FileTarget("logfile")
            {
                FileName = Path.Combine(DirectoryToolkit.GetCocoLogsDir(), "Application.log"),
                Layout = "${longdate} [${level:uppercase=true}] ${message}"
            };
            var logConsole = new ColoredConsoleTarget("logconsole")
            {
                Layout = "${longdate} [${level:uppercase=true}] ${message}"
            };

            var logFileAsync = new AsyncTargetWrapper(logFile);
            var logConsoleAsync = new AsyncTargetWrapper(logConsole);

            configuration.AddRule(LogLevel.Info, LogLevel.Fatal, logFileAsync);
            configuration.AddRule(LogLevel.Info, LogLevel.Fatal, logConsoleAsync);

            LogManager.Configuration = configuration;
        }

        public static void Empty()
        {
            if (Directory.Exists(DirectoryToolkit.GetCocoLogsDir()))
            {
                var directory = new DirectoryInfo(DirectoryToolkit.GetCocoLogsDir());

                foreach (var file in directory.GetFiles())
                {
                    file.Delete();
                }
            }
        }

        public static void Info(string message)
        {
            Logger.Info(message);
        }

        public static void Warning(string message)
        {
            Logger.Warn(message);
        }

        public static void Error(string message)
        {
            Logger.Error(message);
        }
    }
}
