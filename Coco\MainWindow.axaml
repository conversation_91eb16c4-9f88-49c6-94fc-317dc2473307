<FluentWindow xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:pages="using:Coco.Pages"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        x:Class="Coco.MainWindow"
        MinWidth="800" MaxWidth="800"
        Title="Coco" Icon="avares://Coco/Assets/AppLogo.ico"
        WindowStartupLocation="CenterScreen">
  <FluentWindow.Styles>
    <Style Selector="Border.panel">
      <Setter Property="Background" Value="#001529" />
      <Setter Property="Width" Value="80" />
    </Style>

    <Style Selector="TextBlock.title">
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="20" />
      <Setter Property="FontWeight" Value="Regular" />
      <Setter Property="Foreground" Value="#85000000" />
    </Style>
    
    <Style Selector="TextBlock.side">
      <Setter Property="FontFamily" Value="{StaticResource DefaultFont}" />
      <Setter Property="FontSize" Value="12" />
      <Setter Property="Margin" Value="0,5,0,0" />
      <Setter Property="HorizontalAlignment" Value="Center" />
    </Style>
    
    <Style Selector="TextBlock.speed">
      <Setter Property="FontFamily" Value="{StaticResource EnglishFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="Foreground" Value="#85000000" />
      <Setter Property="Margin" Value="3,0,0,0" />
    </Style>

    <Style Selector="TextBlock.version">
      <Setter Property="FontFamily" Value="{StaticResource EnglishFont}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="Foreground" Value="#FFFFFF" />
      <Setter Property="Margin" Value="0,0,0,15" />
      <Setter Property="HorizontalAlignment" Value="Center" />
      <Setter Property="VerticalAlignment" Value="Bottom" />
    </Style>
    
    <Style Selector="TabItem.panel">
      <Setter Property="Cursor" Value="Hand" />
      <Setter Property="Foreground" Value="#FFFFFF" />
    </Style>
    
    <Style Selector="Button.anno">
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="Margin" Value="0,40,0,0" />
      <Setter Property="HorizontalAlignment" Value="Center" />
      <Setter Property="VerticalAlignment" Value="Top" />
    </Style>
    
    <Style Selector="PathIcon.bell">
      <Setter Property="Foreground" Value="#FFFFFF" />
      <Setter Property="Width" Value="30" />
      <Setter Property="Height" Value="30" />
    </Style>
    
    <Style Selector="Path.side">
      <Setter Property="Fill" Value="White" />
      <Setter Property="Stretch" Value="Fill" />
      <Setter Property="Width" Value="28" />
      <Setter Property="Height" Value="24" />
    </Style>
    
    <Style Selector="Path.side2">
      <Setter Property="Fill" Value="White" />
      <Setter Property="Stretch" Value="Fill" />
      <Setter Property="Width" Value="24" />
      <Setter Property="Height" Value="24" />
    </Style>
    
    <Style Selector="Path.uplink">
      <Setter Property="Fill" Value="Red" />
      <Setter Property="Stretch" Value="Fill" />
      <Setter Property="Width" Value="10" />
      <Setter Property="Height" Value="10" />
      <Setter Property="Margin" Value="0,5,0,0" />
      <Setter Property="VerticalAlignment" Value="Top" />
    </Style>

    <Style Selector="Path.downlink">
      <Setter Property="Fill" Value="Green" />
      <Setter Property="Stretch" Value="Fill" />
      <Setter Property="Width" Value="10" />
      <Setter Property="Height" Value="10" />
      <Setter Property="Margin" Value="0,5,0,0" />
      <Setter Property="VerticalAlignment" Value="Top" />
    </Style>

    <Style Selector="TabItem.panel:pointerover /template/ Border">
      <Setter Property="Background" Value="Transparent" />
    </Style>
    
    <Style Selector="TabItem.panel:selected /template/ Border">
      <Setter Property="Background" Value="#1890FF" />
    </Style>
    
    <Style Selector="TabItem.panel:pointerover /template/ ContentPresenter">
      <Setter Property="TextBlock.Foreground" Value="White" />
    </Style>
  
    <Style Selector="Button.anno:focus /template/ ContentPresenter">
      <Setter Property="Background" Value="Transparent" />
    </Style>
    
    <Style Selector="Button.anno:pointerover /template/ ContentPresenter">
      <Setter Property="Background" Value="Transparent" />
    </Style>
      
    <Style Selector="Button.anno:pressed /template/ ContentPresenter">
      <Setter Property="Background" Value="Transparent" />
    </Style>
  </FluentWindow.Styles>
  
  <Panel>
    <DockPanel HorizontalAlignment="Stretch">
      <Border Classes="panel" DockPanel.Dock="Left">
        <Grid>
          <Grid.RowDefinitions>
            <RowDefinition />
            
            <RowDefinition />
          </Grid.RowDefinitions>
        
          <!-- 公告按钮 -->
          <Button Classes="anno" Click="AnnoButton_Click">
            <PathIcon Classes="bell" Data="M816 768h-24V428c0-141.1-104.3-257.7-240-277.1V112c0-22.1-17.9-40-40-40s-40 17.9-40 40v38.9c-135.7 19.4-240 136-240 277.1v340h-24c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h216c0 61.8 50.2 112 112 112s112-50.2 112-112h216c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM512 888c-26.5 0-48-21.5-48-48h96c0 26.5-21.5 48-48 48zM304 768V428c0-55.6 21.6-107.8 60.9-147.1S456.4 220 512 220c55.6 0 107.8 21.6 147.1 60.9S720 372.4 720 428v340H304z" />
          </Button>
        
          <!-- 版本信息-->
          <TextBlock Grid.Row="1" Classes="version" Text="v0.0.2" />
        </Grid>
      </Border>

      <Grid Background="White" IsHitTestVisible="False">
        <Grid.ColumnDefinitions>
          <ColumnDefinition />

          <ColumnDefinition />
        </Grid.ColumnDefinitions>

        <TextBlock Classes="title" Text="{Binding #TabControl.SelectedItem.Tag}" Margin="20,16,0,0" />

        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,25,20,0">
          <!-- 上行速度 -->
          <StackPanel Orientation="Horizontal">
            <Path Classes="uplink" Data="M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z" />

            <TextBlock x:Name="UploadSpeed" Classes="speed" Text="0.00K" />
          </StackPanel>

          <!-- 下行速度 -->
          <StackPanel Orientation="Horizontal" Margin="5,0,0,0">
            <Path Classes="downlink" Data="M862 465.3h-81c-4.6 0-9 2-12.1 5.5L550 723.1V160c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v563.1L255.1 470.8c-3-3.5-7.4-5.5-12.1-5.5h-81c-6.8 0-10.5 8.1-6 13.2L487.9 861a31.96 31.96 0 0048.3 0L868 478.5c4.5-5.2.8-13.2-6-13.2z" />
            <TextBlock x:Name="DownloadSpeed" Classes="speed" Text="0.00K" />
          </StackPanel>
        </StackPanel>
      </Grid>
    </DockPanel>

    <TabControl x:Name="TabControl" Classes="sidebar">
      <!-- 加速页面 -->
      <TabItem Classes="panel" Tag="加速">
        <TabItem.Header>
          <StackPanel Orientation="Vertical" Margin="-10,0,0,0">
            <Path Classes="side" Data="M924.8 385.6a446.7 446.7 0 00-96-142.4 446.7 446.7 0 00-142.4-96C631.1 123.8 572.5 112 512 112s-119.1 11.8-174.4 35.2a446.7 446.7 0 00-142.4 96 446.7 446.7 0 00-96 142.4C75.8 440.9 64 499.5 64 560c0 132.7 58.3 257.7 159.9 343.1l1.7 1.4c5.8 4.8 13.1 7.5 20.6 7.5h531.7c7.5 0 14.8-2.7 20.6-7.5l1.7-1.4C901.7 817.7 960 692.7 960 560c0-60.5-11.9-119.1-35.2-174.4zM482 232c0-4.4 3.6-8 8-8h44c4.4 0 8 3.6 8 8v80c0 4.4-3.6 8-8 8h-44c-4.4 0-8-3.6-8-8v-80zM270 582c0 4.4-3.6 8-8 8h-80c-4.4 0-8-3.6-8-8v-44c0-4.4 3.6-8 8-8h80c4.4 0 8 3.6 8 8v44zm90.7-204.5l-31.1 31.1a8.03 8.03 0 01-11.3 0L261.7 352a8.03 8.03 0 010-11.3l31.1-31.1c3.1-3.1 8.2-3.1 11.3 0l56.6 56.6c3.1 3.1 3.1 8.2 0 11.3zm291.1 83.6l-84.5 84.5c5 18.7.2 39.4-14.5 54.1a55.95 55.95 0 01-79.2 0 55.95 55.95 0 010-79.2 55.87 55.87 0 0154.1-14.5l84.5-84.5c3.1-3.1 8.2-3.1 11.3 0l28.3 28.3c3.1 3.1 3.1 8.1 0 11.3zm43-52.4l-31.1-31.1a8.03 8.03 0 010-11.3l56.6-56.6c3.1-3.1 8.2-3.1 11.3 0l31.1 31.1c3.1 3.1 3.1 8.2 0 11.3l-56.6 56.6a8.03 8.03 0 01-11.3 0zM846 582c0 4.4-3.6 8-8 8h-80c-4.4 0-8-3.6-8-8v-44c0-4.4 3.6-8 8-8h80c4.4 0 8 3.6 8 8v44z" />
            <TextBlock Classes="side" Text="加速" />
          </StackPanel>
        </TabItem.Header>
        
        <Grid Margin="0,60,0,0">
          <pages:AccPage />
        </Grid>
      </TabItem>

      <!-- 个人页面 -->
      <TabItem Classes="panel" Tag="个人中心">
        <TabItem.Header>
          <StackPanel Orientation="Vertical" Margin="-10,0,0,0">
            <Path Classes="side2" Data="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z" />
            <TextBlock Classes="side" Text="个人中心" />
          </StackPanel>
        </TabItem.Header>
        
        <Grid Margin="0,60,0,0">
          <pages:HomePage />
        </Grid>
      </TabItem>
    </TabControl>
  </Panel>
</FluentWindow>
